import * as Sentry from '@sentry/react-native';
import {
  AuthRequestConfig,
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  makeRedirectUri,
  refreshAsync,
  ResponseType,
  revokeAsync,
  TokenResponse,
  useAuthRequest,
} from "expo-auth-session";
import * as SecureStore from "expo-secure-store";
import * as WebBrowser from "expo-web-browser";
import { jwtDecode } from "jwt-decode";
import React, {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Alert } from "react-native";
import { EnvironmentConfig } from "./services/discoveryService";

WebBrowser.maybeCompleteAuthSession();

// No default config - only use discovered environment

const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});
console.log("redirectUri:", redirectUri);

/**
 * Create discovery document from environment configuration
 */
const createDiscoveryDocument = (config: EnvironmentConfig): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isConfigured: boolean; // Whether environment has been configured
  isAuthReady: boolean; // Whether useAuthRequest is ready with valid config
  clearEnvironmentConfig?: () => void; // Function to clear environment config
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper functions to manage tokens in SecureStore
const saveTokens = async (tokens: TokenResponse | null) => {
  if (tokens) {
    await SecureStore.setItemAsync("authTokens", JSON.stringify(tokens));
  } else {
    await SecureStore.deleteItemAsync("authTokens");
  }
};

const getTokens = async (): Promise<TokenResponse | null> => {
  const tokensString = await SecureStore.getItemAsync("authTokens");
  return tokensString ? JSON.parse(tokensString) : null;
};

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

interface AuthProviderProps extends PropsWithChildren {
  environmentConfig?: EnvironmentConfig | null;
  onClearEnvironmentConfig?: () => void;
}

/**
 * Provider that wraps your app with the AuthContext.
 * It handles the expo-auth-session for Cognito and manages token persistence.
 * 
 * @param environmentConfig - Dynamic environment configuration from discovery service
 */
export function AuthProvider({ children, environmentConfig = null, onClearEnvironmentConfig }: AuthProviderProps) {
  const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeAuthRequest, setActiveAuthRequest] = useState<any>(null);
  const [isOAuthFlowActive, setIsOAuthFlowActive] = useState(false);
  const [processedResponseUrl, setProcessedResponseUrl] = useState<string | null>(null);

  // Only use real environment config, no fallback
  const config = environmentConfig;
  const isConfigured = !!environmentConfig;

  console.log("AuthProvider environmentConfig:", environmentConfig?.userPoolUrl || "null");
  
  // Create stable configuration for OAuth flow - don't recreate during active flows
  const stableConfig = useMemo(() => {
    // If OAuth flow is active, keep using the existing config to preserve PKCE parameters
    if (isOAuthFlowActive && activeAuthRequest?.config) {
      console.log("OAuth flow active, using stable config to preserve PKCE");
      return activeAuthRequest.config;
    }
    // Otherwise, use the current config
    return config;
  }, [config, isOAuthFlowActive, activeAuthRequest?.config]);

  // Create discovery document from stable config (only when config exists)
  const discovery = useMemo(() =>
    stableConfig ? createDiscoveryDocument(stableConfig) : null,
    [stableConfig]
  );

  const requestConfig: AuthRequestConfig = useMemo(() =>
    stableConfig ? ({
      clientId: stableConfig.clientId,
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    }) : ({
      clientId: "", // Dummy config when not ready
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    }),
    [stableConfig?.clientId]
  );

  const [request, response, _promptAsync] = useAuthRequest(
    requestConfig,
    discovery
  );

  console.log("useAuthRequest recreated, request ID:", request?.url?.slice(-10));

  // Store the auth request when it's first created with valid config AND OAuth flow is active
  useEffect(() => {
    if (request && stableConfig && !activeAuthRequest && isOAuthFlowActive) {
      console.log("Storing active auth request for PKCE preservation");
      setActiveAuthRequest({
        request,
        config: stableConfig
      });
    }
  }, [request, stableConfig, activeAuthRequest, isOAuthFlowActive]);

  // Track if auth request is ready with valid config
  const isAuthReady = useMemo(() => {
    return !!(config && request && discovery);
  }, [config, request, discovery]);

  console.log("Auth ready state:", isAuthReady, "config:", !!config, "request:", !!request, "discovery:", !!discovery);

  // Wrap promptAsync to prevent calls when config isn't ready and track OAuth flow
  const promptAsync = useMemo(() => {
    if (!isAuthReady) {
      return () => Promise.reject(new Error("Environment not configured yet"));
    }

    return async () => {
      console.log("Starting OAuth flow, preserving PKCE parameters");
      setIsOAuthFlowActive(true);
      try {
        return await _promptAsync();
      } catch (error) {
        console.log("OAuth flow failed, clearing active state");
        setIsOAuthFlowActive(false);
        throw error;
      }
    };
  }, [isAuthReady, _promptAsync]);

  // Load tokens from SecureStore on initial mount
  // Inside AuthProvider's useEffect for initial load
  useEffect(() => {
    const loadTokens = async () => {
      try {
        const tokens = await getTokens();
        if (tokens && config && discovery) {
          console.log("Loading existing tokens for config:", config.userPoolUrl);
          const expirationTime = tokens.issuedAt + (tokens.expiresIn || 0);
          const currentTime = Math.floor(Date.now() / 1000);

          if (currentTime > expirationTime) {
            // Attempt refresh if expired
            try {
              const newTokens = await refreshAsync(
                { clientId: config.clientId, refreshToken: tokens.refreshToken },
                discovery
              );
              if (!newTokens.refreshToken) {
                newTokens.refreshToken = tokens.refreshToken;
              }
              setAuthTokens(newTokens);
              setSentryUserContext(newTokens);
            } catch (error) {
              console.error("Token refresh failed:", error);
              await saveTokens(null);
              setAuthTokens(null);
              setSentryUserContext(null);
            }
          } else {
            setAuthTokens(tokens);
            setSentryUserContext(tokens);
          }
        }
      } catch (error) {
        console.error("Error loading tokens:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      } finally {
        setIsLoading(false);
      }
    };
    loadTokens();
  }, [config?.clientId, discovery]);

  useEffect(() => {
    if (!authTokens || !authTokens.expiresIn || !config || !discovery) return;
  
    const expirationTime = authTokens.issuedAt + authTokens.expiresIn;
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = expirationTime - currentTime;
  
    const refreshBuffer = 300;
    const refreshDelay = Math.max(0, (timeUntilExpiry - refreshBuffer) * 1000);
  
    const timeoutId = setTimeout(async () => {
      try {
        const newTokens = await refreshAsync(
          { clientId: config.clientId, refreshToken: authTokens.refreshToken },
          discovery
        );
        if (!newTokens.refreshToken) {
          newTokens.refreshToken = authTokens.refreshToken;
        }
        setAuthTokens(newTokens);
        setSentryUserContext(newTokens);
      } catch (error) {
        console.error("Background refresh failed:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      }
    }, refreshDelay);
  
    return () => clearTimeout(timeoutId);
  }, [authTokens, config?.clientId, discovery]);  

  // Save tokens to SecureStore whenever they change
  useEffect(() => {
    if (!isLoading) {
      saveTokens(authTokens);
    }
  }, [authTokens, isLoading]);

  // Exchange the authorization code for tokens when response is "success"
  useEffect(() => {
    async function exchangeToken() {
      if (!response) return;

      // Prevent duplicate processing of the same response
      const responseIdentifier = response.type === "success"
        ? `success-${(response as any).params?.code}`
        : `${response.type}-${Date.now()}`;

      if (processedResponseUrl === responseIdentifier) {
        console.log("Response already processed, skipping token exchange");
        return;
      }

      // Use the preserved auth request for token exchange to maintain PKCE consistency
      const authRequestToUse = activeAuthRequest?.request || request;
      const configToUse = activeAuthRequest?.config || config;

      if (!authRequestToUse || !configToUse || !discovery) {
        console.log("Missing required data for token exchange:", {
          authRequest: !!authRequestToUse,
          config: !!configToUse,
          discovery: !!discovery
        });
        return;
      }

      if (response.type === "success") {
        // Mark this response as being processed
        setProcessedResponseUrl(responseIdentifier);
        try {
          console.log("Token exchange using preserved auth request");
          console.log("Token exchange using redirectUri:", redirectUri);
          console.log("Token exchange using clientId:", configToUse.clientId);
          console.log("Token exchange using userPoolUrl:", configToUse.userPoolUrl);
          console.log("Token exchange using request ID:", authRequestToUse.url?.slice(-10));

          const exchangeResponse = await exchangeCodeAsync(
            {
              code: response.params.code,
              clientId: configToUse.clientId,
              redirectUri,
              extraParams: {
                code_verifier: authRequestToUse.codeVerifier || "",
              },
            },
            discovery
          );

          setAuthTokens(exchangeResponse);
          setSentryUserContext(exchangeResponse);

          // Clear OAuth flow state after successful token exchange
          console.log("Token exchange successful, clearing OAuth flow state");
          setIsOAuthFlowActive(false);
          setActiveAuthRequest(null);
        } catch (error) {
          console.error("Error exchanging code:", error);
          // Clear OAuth flow state on error too
          setIsOAuthFlowActive(false);
          Alert.alert(
            "Error",
            "Failed to exchange authorization code for tokens"
          );
        }
      } else if (response.type === "error") {
        // Mark error response as processed
        setProcessedResponseUrl(responseIdentifier);
        console.log("OAuth error, clearing flow state");
        setIsOAuthFlowActive(false);
        Alert.alert(
          "Authentication error",
          (response as any).params?.error_description || "Something went wrong"
        );
      }
    }

    exchangeToken();
  }, [response, processedResponseUrl]); // Simplified dependencies to prevent re-triggering after cleanup

  // Clear OAuth flow state when environment config is cleared
  useEffect(() => {
    if (!config) {
      console.log("Environment config cleared, resetting OAuth flow state");
      setIsOAuthFlowActive(false);
      setActiveAuthRequest(null);
      setProcessedResponseUrl(null);
    }
  }, [config]);

  // Logout -> revoke tokens and clear SecureStore
  const logout = async (): Promise<void> => {
    if (!authTokens?.refreshToken || !config || !discovery) {
      setAuthTokens(null);
      setSentryUserContext(null);
      return;
    }

    try {
      // 1) Open the Cognito logout page in an AuthSession (web browser)
      const logoutUrl = `${config.userPoolUrl}/logout?client_id=${config.clientId}&logout_uri=${encodeURIComponent(redirectUri)}`;
      const result = await WebBrowser.openAuthSessionAsync(logoutUrl);
      if (result.type === "success") {
        // 2) Revoke the refresh token so it's invalidated
        await revokeAsync(
          {
            clientId: config.clientId,
            token: authTokens.refreshToken,
          },
          discovery
        );
        setAuthTokens(null);
        setSentryUserContext(null);
        // Clear environment config on successful logout
        onClearEnvironmentConfig?.();
      }
      // If cancelled, do nothing
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Memoize the context value for performance
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      isLoading,
      isConfigured, // Expose whether environment is configured
      isAuthReady, // Expose whether auth request is ready
      clearEnvironmentConfig: onClearEnvironmentConfig,
    }),
    [authTokens, promptAsync, logout, isLoading, isConfigured, isAuthReady, onClearEnvironmentConfig]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}
