import * as Sentry from '@sentry/react-native';
import {
  AuthRequestConfig,
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  makeRedirectUri,
  refreshAsync,
  ResponseType,
  revokeAsync,
  TokenResponse,
  useAuthRequest,
} from "expo-auth-session";
import * as SecureStore from "expo-secure-store";
import * as WebBrowser from "expo-web-browser";
import { jwtDecode } from "jwt-decode";
import React, {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Alert } from "react-native";
import { EnvironmentConfig } from "./services/discoveryService";

WebBrowser.maybeCompleteAuthSession();

// No default config - only use discovered environment

const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});
console.log("redirectUri:", redirectUri);

/**
 * Create discovery document from environment configuration
 */
const createDiscoveryDocument = (config: EnvironmentConfig): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isConfigured: boolean; // Whether environment has been configured
  isAuthReady: boolean; // Whether useAuthRequest is ready with valid config
  clearEnvironmentConfig?: () => void; // Function to clear environment config
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper functions to manage tokens in SecureStore
const saveTokens = async (tokens: TokenResponse | null) => {
  if (tokens) {
    await SecureStore.setItemAsync("authTokens", JSON.stringify(tokens));
  } else {
    await SecureStore.deleteItemAsync("authTokens");
  }
};

const getTokens = async (): Promise<TokenResponse | null> => {
  const tokensString = await SecureStore.getItemAsync("authTokens");
  return tokensString ? JSON.parse(tokensString) : null;
};

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

interface AuthProviderProps extends PropsWithChildren {
  environmentConfig?: EnvironmentConfig | null;
  onClearEnvironmentConfig?: () => void;
}

/**
 * Provider that wraps your app with the AuthContext.
 * It handles the expo-auth-session for Cognito and manages token persistence.
 * 
 * @param environmentConfig - Dynamic environment configuration from discovery service
 */
export function AuthProvider({ children, environmentConfig = null, onClearEnvironmentConfig }: AuthProviderProps) {
  const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Only use real environment config, no fallback
  const config = environmentConfig;
  const isConfigured = !!environmentConfig;
  
  console.log("AuthProvider environmentConfig:", environmentConfig?.userPoolUrl || "null");
  
  // Create discovery document from current config (only when config exists)
  const discovery = useMemo(() => 
    config ? createDiscoveryDocument(config) : null, 
    [config]
  );

  const requestConfig: AuthRequestConfig = useMemo(() => 
    config ? ({
      clientId: config.clientId,
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    }) : ({
      clientId: "", // Dummy config when not ready
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    }), 
    [config?.clientId]
  );

  const [request, response, _promptAsync] = useAuthRequest(
    requestConfig,
    discovery
  );

  console.log("useAuthRequest recreated, request ID:", request?.url?.slice(-10));

  // Track if auth request is ready with valid config
  const isAuthReady = useMemo(() => {
    return !!(config && request && discovery);
  }, [config, request, discovery]);

  console.log("Auth ready state:", isAuthReady, "config:", !!config, "request:", !!request, "discovery:", !!discovery);

  // Wrap promptAsync to prevent calls when config isn't ready
  const promptAsync = useMemo(() => {
    if (!isAuthReady) {
      return () => Promise.reject(new Error("Environment not configured yet"));
    }
    return _promptAsync;
  }, [isAuthReady, _promptAsync]);

  // Load tokens from SecureStore on initial mount
  // Inside AuthProvider's useEffect for initial load
  useEffect(() => {
    const loadTokens = async () => {
      try {
        const tokens = await getTokens();
        if (tokens && config && discovery) {
          console.log("Loading existing tokens for config:", config.userPoolUrl);
          const expirationTime = tokens.issuedAt + (tokens.expiresIn || 0);
          const currentTime = Math.floor(Date.now() / 1000);

          if (currentTime > expirationTime) {
            // Attempt refresh if expired
            try {
              const newTokens = await refreshAsync(
                { clientId: config.clientId, refreshToken: tokens.refreshToken },
                discovery
              );
              if (!newTokens.refreshToken) {
                newTokens.refreshToken = tokens.refreshToken;
              }
              setAuthTokens(newTokens);
              setSentryUserContext(newTokens);
            } catch (error) {
              console.error("Token refresh failed:", error);
              await saveTokens(null);
              setAuthTokens(null);
              setSentryUserContext(null);
            }
          } else {
            setAuthTokens(tokens);
            setSentryUserContext(tokens);
          }
        }
      } catch (error) {
        console.error("Error loading tokens:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      } finally {
        setIsLoading(false);
      }
    };
    loadTokens();
  }, [config?.clientId, discovery]);

  useEffect(() => {
    if (!authTokens || !authTokens.expiresIn || !config || !discovery) return;
  
    const expirationTime = authTokens.issuedAt + authTokens.expiresIn;
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = expirationTime - currentTime;
  
    const refreshBuffer = 300;
    const refreshDelay = Math.max(0, (timeUntilExpiry - refreshBuffer) * 1000);
  
    const timeoutId = setTimeout(async () => {
      try {
        const newTokens = await refreshAsync(
          { clientId: config.clientId, refreshToken: authTokens.refreshToken },
          discovery
        );
        if (!newTokens.refreshToken) {
          newTokens.refreshToken = authTokens.refreshToken;
        }
        setAuthTokens(newTokens);
        setSentryUserContext(newTokens);
      } catch (error) {
        console.error("Background refresh failed:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      }
    }, refreshDelay);
  
    return () => clearTimeout(timeoutId);
  }, [authTokens, config?.clientId, discovery]);  

  // Save tokens to SecureStore whenever they change
  useEffect(() => {
    if (!isLoading) {
      saveTokens(authTokens);
    }
  }, [authTokens, isLoading]);

  // Exchange the authorization code for tokens when response is "success"
  useEffect(() => {
    async function exchangeToken() {
      if (!request || !response || !config || !discovery) return;

      if (response.type === "success") {
        try {
          console.log("Token exchange using redirectUri:", redirectUri);
          console.log("Token exchange using clientId:", config.clientId);
          console.log("Token exchange using userPoolUrl:", config.userPoolUrl);
          
          const exchangeResponse = await exchangeCodeAsync(
            {
              code: response.params.code,
              clientId: config.clientId,
              redirectUri,
              extraParams: {
                code_verifier: request.codeVerifier || "",
              },
            },
            discovery
          );

          setAuthTokens(exchangeResponse);
          setSentryUserContext(exchangeResponse);
        } catch (error) {
          console.error("Error exchanging code:", error);
          Alert.alert(
            "Error",
            "Failed to exchange authorization code for tokens"
          );
        }
      } else if (response.type === "error") {
        Alert.alert(
          "Authentication error",
          response.params.error_description || "Something went wrong"
        );
      }
    }

    exchangeToken();
  }, [request, response, config?.clientId, discovery]);

  // Logout -> revoke tokens and clear SecureStore
  const logout = async (): Promise<void> => {
    if (!authTokens?.refreshToken || !config || !discovery) {
      setAuthTokens(null);
      setSentryUserContext(null);
      return;
    }

    try {
      // 1) Open the Cognito logout page in an AuthSession (web browser)
      const logoutUrl = `${config.userPoolUrl}/logout?client_id=${config.clientId}&logout_uri=${encodeURIComponent(redirectUri)}`;
      const result = await WebBrowser.openAuthSessionAsync(logoutUrl);
      if (result.type === "success") {
        // 2) Revoke the refresh token so it's invalidated
        await revokeAsync(
          {
            clientId: config.clientId,
            token: authTokens.refreshToken,
          },
          discovery
        );
        setAuthTokens(null);
        setSentryUserContext(null);
        // Clear environment config on successful logout
        onClearEnvironmentConfig?.();
      }
      // If cancelled, do nothing
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Memoize the context value for performance
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      isLoading,
      isConfigured, // Expose whether environment is configured
      isAuthReady, // Expose whether auth request is ready
      clearEnvironmentConfig: onClearEnvironmentConfig,
    }),
    [authTokens, promptAsync, logout, isLoading, isConfigured, isAuthReady, onClearEnvironmentConfig]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}
