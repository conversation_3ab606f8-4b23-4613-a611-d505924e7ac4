import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  PropsWithChildren,
} from "react";
import {
  AuthRequestConfig,
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  revokeAsync,
  ResponseType,
  TokenResponse,
  useAuthRequest,
  refreshAsync,
} from "expo-auth-session";
import * as Web<PERSON>rowser from "expo-web-browser";
import * as SecureStore from "expo-secure-store";
import { Alert } from "react-native";
import * as Sentry from '@sentry/react-native';
import { jwtDecode } from "jwt-decode";

WebBrowser.maybeCompleteAuthSession();

const clientId = "3fv7mur82c02q30o7oflj0u5ub";
const userPoolUrl = "https://auth.demo-1.gethero.com";
const redirectUri = "myapp://callback/";

const discovery: DiscoveryDocument = {
  authorizationEndpoint: `${userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${userPoolUrl}/oauth2/revoke`,
};

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  isLoading: boolean; // Add loading state to context
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper functions to manage tokens in SecureStore
const saveTokens = async (tokens: TokenResponse | null) => {
  if (tokens) {
    await SecureStore.setItemAsync("authTokens", JSON.stringify(tokens));
  } else {
    await SecureStore.deleteItemAsync("authTokens");
  }
};

const getTokens = async (): Promise<TokenResponse | null> => {
  const tokensString = await SecureStore.getItemAsync("authTokens");
  return tokensString ? JSON.parse(tokensString) : null;
};

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

/**
 * Provider that wraps your app with the AuthContext.
 * It handles the expo-auth-session for Cognito and manages token persistence.
 */
export function AuthProvider({ children }: PropsWithChildren) {
  const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Add loading state

  const requestConfig: AuthRequestConfig = {
    clientId,
    responseType: ResponseType.Code,
    redirectUri,
    usePKCE: true,
  };

  const [request, response, promptAsync] = useAuthRequest(
    requestConfig,
    discovery
  );

  // Load tokens from SecureStore on initial mount
  // Inside AuthProvider's useEffect for initial load
  useEffect(() => {
    const loadTokens = async () => {
      try {
        const tokens = await getTokens();
        if (tokens) {
          const expirationTime = tokens.issuedAt + (tokens.expiresIn || 0);
          const currentTime = Math.floor(Date.now() / 1000);

          if (currentTime > expirationTime) {
            // Attempt refresh if expired
            try {
              const newTokens = await refreshAsync(
                { clientId, refreshToken: tokens.refreshToken },
                discovery
              );
              if (!newTokens.refreshToken) {
                newTokens.refreshToken = tokens.refreshToken;
              }
              setAuthTokens(newTokens);
              setSentryUserContext(newTokens);
            } catch (error) {
              console.error("Token refresh failed:", error);
              await saveTokens(null);
              setAuthTokens(null);
              setSentryUserContext(null);
            }
          } else {
            setAuthTokens(tokens);
            setSentryUserContext(tokens);
          }
        }
      } catch (error) {
        console.error("Error loading tokens:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      } finally {
        setIsLoading(false);
      }
    };
    loadTokens();
  }, []);

  useEffect(() => {
    if (!authTokens || !authTokens.expiresIn) return;
  
    const expirationTime = authTokens.issuedAt + authTokens.expiresIn;
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = expirationTime - currentTime;
  
    const refreshBuffer = 300;
    const refreshDelay = Math.max(0, (timeUntilExpiry - refreshBuffer) * 1000);
  
    const timeoutId = setTimeout(async () => {
      try {
        const newTokens = await refreshAsync(
          { clientId, refreshToken: authTokens.refreshToken },
          discovery
        );
        if (!newTokens.refreshToken) {
          newTokens.refreshToken = authTokens.refreshToken;
        }
        setAuthTokens(newTokens);
        setSentryUserContext(newTokens);
      } catch (error) {
        console.error("Background refresh failed:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      }
    }, refreshDelay);
  
    return () => clearTimeout(timeoutId);
  }, [authTokens]);  

  // Save tokens to SecureStore whenever they change
  useEffect(() => {
    if (!isLoading) {
      saveTokens(authTokens);
    }
  }, [authTokens, isLoading]);

  // Exchange the authorization code for tokens when response is "success"
  useEffect(() => {
    async function exchangeToken() {
      if (!request || !response) return;

      if (response.type === "success") {
        try {
          const exchangeResponse = await exchangeCodeAsync(
            {
              code: response.params.code,
              clientId,
              redirectUri,
              extraParams: {
                code_verifier: request.codeVerifier || "",
              },
            },
            discovery
          );

          setAuthTokens(exchangeResponse);
          setSentryUserContext(exchangeResponse);
        } catch (error) {
          console.error("Error exchanging code:", error);
          Alert.alert(
            "Error",
            "Failed to exchange authorization code for tokens"
          );
        }
      } else if (response.type === "error") {
        Alert.alert(
          "Authentication error",
          response.params.error_description || "Something went wrong"
        );
      }
    }

    exchangeToken();
  }, [request, response]);

  // Logout -> revoke tokens and clear SecureStore
  const logout = async (): Promise<void> => {
    if (!authTokens?.refreshToken) {
      setAuthTokens(null);
      setSentryUserContext(null);
      return;
    }

    try {
      // 1) Open the Cognito logout page in an AuthSession (web browser)
      const logoutUrl = `${userPoolUrl}/logout?client_id=${clientId}&logout_uri=${encodeURIComponent(redirectUri)}`;
      const result = await WebBrowser.openAuthSessionAsync(logoutUrl);
      if (result.type === "success") {
        // 2) Revoke the refresh token so it's invalidated
        await revokeAsync(
          {
            clientId,
            token: authTokens.refreshToken,
          },
          discovery
        );
        setAuthTokens(null);
        setSentryUserContext(null);
      }
      // If cancelled, do nothing
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Memoize the context value for performance
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      isLoading, // Expose loading state
    }),
    [authTokens, promptAsync, isLoading]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}
