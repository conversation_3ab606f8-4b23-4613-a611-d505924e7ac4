import * as Sentry from '@sentry/react-native';
import {
  AuthRequestConfig,
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  makeRedirectUri,
  refreshAsync,
  ResponseType,
  revokeAsync,
  TokenResponse,
  useAuthRequest,
} from "expo-auth-session";
import * as SecureStore from "expo-secure-store";
import * as WebBrowser from "expo-web-browser";
import { jwtDecode } from "jwt-decode";
import React, {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Alert } from "react-native";
import { EnvironmentConfig } from "./services/discoveryService";

WebBrowser.maybeCompleteAuthSession();

// No default config - only use discovered environment

const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});
console.log("redirectUri:", redirectUri);

/**
 * Create discovery document from environment configuration
 */
const createDiscoveryDocument = (config: EnvironmentConfig): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isConfigured: boolean; // Whether environment has been configured
  isAuthReady: boolean; // Whether useAuthRequest is ready with valid config
  clearEnvironmentConfig?: () => void; // Function to clear environment config
  clearAllAuthData: () => Promise<void>; // Complete auth data clearing for email switching
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper functions to manage tokens in SecureStore
const saveTokens = async (tokens: TokenResponse | null) => {
  if (tokens) {
    await SecureStore.setItemAsync("authTokens", JSON.stringify(tokens));
  } else {
    await SecureStore.deleteItemAsync("authTokens");
  }
};

const getTokens = async (): Promise<TokenResponse | null> => {
  const tokensString = await SecureStore.getItemAsync("authTokens");
  return tokensString ? JSON.parse(tokensString) : null;
};

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

interface AuthProviderProps extends PropsWithChildren {
  environmentConfig?: EnvironmentConfig | null;
  onClearEnvironmentConfig?: () => void;
}

/**
 * Provider that wraps your app with the AuthContext.
 * It handles the expo-auth-session for Cognito and manages token persistence.
 * 
 * @param environmentConfig - Dynamic environment configuration from discovery service
 */
export function AuthProvider({ children, environmentConfig = null, onClearEnvironmentConfig }: AuthProviderProps) {
  const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isOAuthFlowActive, setIsOAuthFlowActive] = useState(false);
  
  // Use refs for tracking to avoid dependency chain issues - expo-auth-session best practice
  const currentConfigRef = useRef<string | null>(null);
  const processedResponsesRef = useRef<Set<string>>(new Set());
  const activePromptConfigRef = useRef<string | null>(null); // Track config when promptAsync was called
  const deferredConfigResetRef = useRef<string | null>(null); // Track if config reset was deferred

  // Use environment config directly - no complex preservation
  const config = environmentConfig;
  const isConfigured = !!environmentConfig;

  console.log("AuthProvider environmentConfig:", environmentConfig?.userPoolUrl || "null");

  // Create discovery document from config (only when config exists)
  const discovery = useMemo(() =>
    config ? createDiscoveryDocument(config) : null,
    [config]
  );

  const requestConfig: AuthRequestConfig = useMemo(() =>
    config ? ({
      clientId: config.clientId,
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    }) : ({
      clientId: "", // Dummy config when not ready
      responseType: ResponseType.Code,
      redirectUri,
      usePKCE: true,
    }),
    [config?.clientId] // Standard dependency
  );

  const [request, response, _promptAsync] = useAuthRequest(
    requestConfig,
    discovery
  );

  console.log("=== useAuthRequest Debug ===");
  console.log("useAuthRequest recreated, request ID:", request?.url?.slice(-10));
  console.log("OAuth flow active:", isOAuthFlowActive);
  console.log("Current config:", config?.userPoolUrl);
  console.log("=============================");

  // Complete auth data clearing for email switching - expo-auth-session best practice
  const clearAllAuthData = async () => {
    console.log("=== Complete Auth Data Clear ===");
    
    try {
      // 1. Revoke refresh token if we have one
      if (authTokens?.refreshToken && config && discovery) {
        console.log("Revoking refresh token with current config");
        await revokeAsync(
          {
            clientId: config.clientId,
            token: authTokens.refreshToken,
          },
          discovery
        );
      }
    } catch (error) {
      console.warn("Failed to revoke token (continuing with cleanup):", error);
    }

    try {
      // 2. Clear SecureStore tokens
      console.log("Clearing SecureStore tokens");
      await SecureStore.deleteItemAsync("authTokens");
    } catch (error) {
      console.warn("Failed to clear SecureStore (continuing):", error);
    }

    // 3. Clear in-memory state
    console.log("Clearing in-memory auth state");
    setAuthTokens(null);
    setSentryUserContext(null);
    setIsOAuthFlowActive(false);
    currentConfigRef.current = null;
    processedResponsesRef.current.clear();
    activePromptConfigRef.current = null;
    deferredConfigResetRef.current = null;
    
    console.log("=== Auth Data Clear Complete ===");
  };

  // Clear all auth data when environment config is cleared (email switching)
  useEffect(() => {
    if (!config && environmentConfig === null) {
      console.log("Environment config cleared, performing complete auth reset");
      clearAllAuthData();
    }
  }, [config, environmentConfig]);

  // Track if auth request is ready with valid config
  const isAuthReady = useMemo(() => {
    return !!(config && request && discovery);
  }, [config, request, discovery]);

  console.log("Auth ready state:", isAuthReady, "config:", !!config, "request:", !!request, "discovery:", !!discovery);

  // Wrap promptAsync to prevent calls when config isn't ready and track OAuth flow
  const promptAsync = useMemo(() => {
    if (!isAuthReady) {
      return () => Promise.reject(new Error("Environment not configured yet"));
    }

    return async () => {
      console.log("=== OAuth Flow Starting ===");
      console.log("Current request ID:", request?.url?.slice(-10));
      console.log("Current config:", config?.userPoolUrl);

      // Track which config is active for this OAuth flow
      const configId = config ? `${config.userPoolUrl}-${config.clientId}` : null;
      activePromptConfigRef.current = configId;
      console.log("Recording active prompt config:", configId);

      setIsOAuthFlowActive(true);
      try {
        const result = await _promptAsync();
        console.log("OAuth flow result:", result.type);
        return result;
      } catch (error) {
        console.log("OAuth flow failed, clearing active state");
        console.error("OAuth error details:", error);
        setIsOAuthFlowActive(false);
        activePromptConfigRef.current = null; // Clear on error
        throw error;
      }
    };
  }, [isAuthReady, _promptAsync, request, config]);

  // Load tokens from SecureStore on initial mount
  // Inside AuthProvider's useEffect for initial load
  useEffect(() => {
    const loadTokens = async () => {
      try {
        const tokens = await getTokens();
        if (tokens && config && discovery) {
          console.log("Loading existing tokens for config:", config.userPoolUrl);
          const expirationTime = tokens.issuedAt + (tokens.expiresIn || 0);
          const currentTime = Math.floor(Date.now() / 1000);

          if (currentTime > expirationTime) {
            // Attempt refresh if expired
            try {
              const newTokens = await refreshAsync(
                { clientId: config.clientId, refreshToken: tokens.refreshToken },
                discovery
              );
              if (!newTokens.refreshToken) {
                newTokens.refreshToken = tokens.refreshToken;
              }
              setAuthTokens(newTokens);
              setSentryUserContext(newTokens);
            } catch (error) {
              console.error("Token refresh failed:", error);
              await saveTokens(null);
              setAuthTokens(null);
              setSentryUserContext(null);
            }
          } else {
            setAuthTokens(tokens);
            setSentryUserContext(tokens);
          }
        }
      } catch (error) {
        console.error("Error loading tokens:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      } finally {
        setIsLoading(false);
      }
    };
    loadTokens();
  }, [config?.clientId, discovery]);

  useEffect(() => {
    if (!authTokens || !authTokens.expiresIn || !config || !discovery) return;
  
    const expirationTime = authTokens.issuedAt + authTokens.expiresIn;
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = expirationTime - currentTime;
  
    const refreshBuffer = 300;
    const refreshDelay = Math.max(0, (timeUntilExpiry - refreshBuffer) * 1000);
  
    const timeoutId = setTimeout(async () => {
      try {
        const newTokens = await refreshAsync(
          { clientId: config.clientId, refreshToken: authTokens.refreshToken },
          discovery
        );
        if (!newTokens.refreshToken) {
          newTokens.refreshToken = authTokens.refreshToken;
        }
        setAuthTokens(newTokens);
        setSentryUserContext(newTokens);
      } catch (error) {
        console.error("Background refresh failed:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      }
    }, refreshDelay);
  
    return () => clearTimeout(timeoutId);
  }, [authTokens, config?.clientId, discovery]);  

  // Save tokens to SecureStore whenever they change
  useEffect(() => {
    if (!isLoading) {
      saveTokens(authTokens);
    }
  }, [authTokens, isLoading]);

  // Exchange the authorization code for tokens when response is "success" - expo-auth-session best practice
  useEffect(() => {
    async function exchangeToken() {
      if (!response) return;

      // Get current config inside effect to avoid dependency issues
      const currentConfig = config ? `${config.userPoolUrl}-${config.clientId}` : null;
      
      // Validate that response belongs to the config that initiated the OAuth flow
      if (!currentConfig || !activePromptConfigRef.current || activePromptConfigRef.current !== currentConfig) {
        console.log("Response config mismatch, skipping token exchange:", {
          activePromptConfig: activePromptConfigRef.current,
          currentConfig: currentConfig,
          responseType: response.type,
          reason: !activePromptConfigRef.current ? "No active prompt config" : "Config mismatch"
        });
        return;
      }

      // Prevent duplicate processing of the same response
      const responseIdentifier = response.type === "success"
        ? `success-${currentConfig}-${(response as any).params?.code}`
        : `${response.type}-${currentConfig}-${Date.now()}`;

      if (processedResponsesRef.current.has(responseIdentifier)) {
        console.log("Response already processed, skipping token exchange");
        return;
      }

      if (!request || !config || !discovery) {
        console.log("Missing required data for token exchange:", {
          authRequest: !!request,
          config: !!config,
          discovery: !!discovery
        });
        return;
      }

      // Additional safety check: ensure request and config are aligned
      if (request && config && request.clientId !== config.clientId) {
        console.log("Request/config mismatch detected, skipping token exchange:", {
          requestClientId: request.clientId,
          configClientId: config.clientId
        });
        return;
      }

      if (response.type === "success") {
        // Mark this response as being processed
        processedResponsesRef.current.add(responseIdentifier);
        try {
          console.log("=== Token Exchange Debug ===");
          console.log("Token exchange request ID:", request.url?.slice(-10));
          console.log("Token exchange using redirectUri:", redirectUri);
          console.log("Token exchange using clientId:", config.clientId);
          console.log("Token exchange using userPoolUrl:", config.userPoolUrl);
          console.log("Token exchange code_verifier:", request.codeVerifier ? "present" : "MISSING");
          console.log("Token exchange code_verifier length:", request.codeVerifier?.length || 0);
          console.log("============================");

          // Validate code_verifier exists - required for PKCE
          if (!request.codeVerifier) {
            console.error("Missing code_verifier - PKCE flow compromised");
            setIsOAuthFlowActive(false);
            activePromptConfigRef.current = null;
            Alert.alert(
              "Authentication Error", 
              "Security validation failed. Please try signing in again."
            );
            return;
          }

          const exchangeResponse = await exchangeCodeAsync(
            {
              code: response.params.code,
              clientId: config.clientId,
              redirectUri,
              extraParams: {
                code_verifier: request.codeVerifier,
              },
            },
            discovery
          );

          setAuthTokens(exchangeResponse);
          setSentryUserContext(exchangeResponse);

          // Clear OAuth flow state after successful token exchange
          console.log("Token exchange successful, clearing OAuth flow state");
          setIsOAuthFlowActive(false);
          activePromptConfigRef.current = null; // Clear the prompt config after successful exchange
        } catch (error) {
          console.error("Error exchanging code:", error);
          // Clear OAuth flow state on error too
          setIsOAuthFlowActive(false);
          activePromptConfigRef.current = null; // Clear the prompt config on error
          Alert.alert(
            "Error",
            "Failed to exchange authorization code for tokens"
          );
        }
      } else if (response.type === "error") {
        // Mark error response as processed
        processedResponsesRef.current.add(responseIdentifier);
        console.log("OAuth error, clearing flow state");
        setIsOAuthFlowActive(false);
        activePromptConfigRef.current = null; // Clear the prompt config on OAuth error
        Alert.alert(
          "Authentication error",
          (response as any).params?.error_description || "Something went wrong"
        );
      }
    }

    exchangeToken();
  }, [response]); // ✅ Clean expo-auth-session best practice - only response dependency

  // Additional OAuth state cleanup (lightweight)
  useEffect(() => {
    if (!config) {
      console.log("Config cleared, resetting OAuth flow state");
      setIsOAuthFlowActive(false);
      currentConfigRef.current = null;
      processedResponsesRef.current.clear();
      activePromptConfigRef.current = null;
      deferredConfigResetRef.current = null;
    }
  }, [config]);

  // Reset OAuth response state when config changes to prevent stale response processing
  useEffect(() => {
    if (config?.userPoolUrl) {
      const newConfigId = `${config.userPoolUrl}-${config.clientId}`;
      
      // Don't reset state during an active OAuth flow to prevent mid-flight request recreation
      if (isOAuthFlowActive) {
        console.log("OAuth flow active, deferring config state reset until flow completes");
        deferredConfigResetRef.current = newConfigId;
        return;
      }
      
      console.log("Config changed, resetting response processing state for new environment:", config.userPoolUrl);
      processedResponsesRef.current.clear(); // Clear all processed responses for fresh start
      currentConfigRef.current = newConfigId;
      deferredConfigResetRef.current = null; // Clear any deferred reset
      // Clear active prompt config when switching environments - old responses should be ignored
      if (activePromptConfigRef.current && activePromptConfigRef.current !== newConfigId) {
        console.log("Clearing active prompt config due to environment switch");
        activePromptConfigRef.current = null;
      }
    } else {
      currentConfigRef.current = null;
      activePromptConfigRef.current = null;
      deferredConfigResetRef.current = null;
    }
  }, [config?.userPoolUrl, config?.clientId, isOAuthFlowActive]);

  // Handle deferred config resets when OAuth flow completes
  useEffect(() => {
    if (!isOAuthFlowActive && deferredConfigResetRef.current) {
      console.log("OAuth flow completed, applying deferred config reset:", deferredConfigResetRef.current);
      processedResponsesRef.current.clear();
      currentConfigRef.current = deferredConfigResetRef.current;
      deferredConfigResetRef.current = null;
    }
  }, [isOAuthFlowActive]);

  // Logout -> revoke tokens and clear SecureStore
  const logout = async (): Promise<void> => {
    if (!authTokens?.refreshToken || !config || !discovery) {
      setAuthTokens(null);
      setSentryUserContext(null);
      return;
    }

    try {
      // 1) Open the Cognito logout page in an AuthSession (web browser)
      const logoutUrl = `${config.userPoolUrl}/logout?client_id=${config.clientId}&logout_uri=${encodeURIComponent(redirectUri)}`;
      const result = await WebBrowser.openAuthSessionAsync(logoutUrl);
      if (result.type === "success") {
        // 2) Revoke the refresh token so it's invalidated
        await revokeAsync(
          {
            clientId: config.clientId,
            token: authTokens.refreshToken,
          },
          discovery
        );
        setAuthTokens(null);
        setSentryUserContext(null);
        // Clear environment config on successful logout
        onClearEnvironmentConfig?.();
      }
      // If cancelled, do nothing
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Memoize the context value for performance
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      isLoading,
      isConfigured, // Expose whether environment is configured
      isAuthReady, // Expose whether auth request is ready
      clearEnvironmentConfig: onClearEnvironmentConfig,
      clearAllAuthData, // Expose complete auth clearing
    }),
    [authTokens, promptAsync, logout, isLoading, isConfigured, isAuthReady, onClearEnvironmentConfig, clearAllAuthData]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}
