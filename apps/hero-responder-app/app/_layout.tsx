import { ActionSheetProvider } from "@expo/react-native-action-sheet";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import * as Sentry from "@sentry/react-native";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack } from "expo-router";
import React, { useEffect } from "react";
import { LogBox } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { NavigationProvider } from "./(app)/V2/contexts/NavigationContext";
import { AuthProvider } from "./AuthContext";
import ErrorBoundary from "./ErrorBoundary";
import { ReportsProvider } from "./ReportContext";
import useLocationStore from "./store/useLocationStore";
import { DarkModeProvider } from "./DarkModeContext";

Sentry.init({
  dsn: "https://<EMAIL>/4509527654793216",

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [
    Sentry.mobileReplayIntegration({
      maskAllText: false,
      maskAllImages: false,
      maskAllVectors: false,
    }),
  ],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

const queryClient = new QueryClient();

export default Sentry.wrap(function RootLayout() {
  LogBox.ignoreAllLogs();

  const startLocationUpdates = useLocationStore(
    (state) => state.startLocationUpdates
  );
  const stopLocationUpdates = useLocationStore(
    (state) => state.stopLocationUpdates
  );

  useEffect(() => {
    startLocationUpdates();
    return () => {
      stopLocationUpdates();
    };
  }, [startLocationUpdates, stopLocationUpdates]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {/* Wrap the entire app in the ErrorBoundary */}
      <ErrorBoundary>
        <DarkModeProvider>
          <QueryClientProvider client={queryClient}>
            <BottomSheetModalProvider>
              <ActionSheetProvider>
                <AuthProvider>
                  <ReportsProvider>
                    <NavigationProvider>
                      <Stack>
                        <Stack.Screen
                          name="(app)"
                          options={{ headerShown: false }}
                        />
                        <Stack.Screen
                          name="sign-in"
                          options={{ headerShown: false }}
                        />
                      </Stack>
                    </NavigationProvider>
                  </ReportsProvider>
                </AuthProvider>
              </ActionSheetProvider>
            </BottomSheetModalProvider>
          </QueryClientProvider>
        </DarkModeProvider>
      </ErrorBoundary>
    </GestureHandlerRootView>
  );
});
