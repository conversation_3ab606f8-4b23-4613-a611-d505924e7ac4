import React, { createContext, useContext, ReactNode } from 'react';
import { EnvironmentConfig } from '../services/discoveryService';

interface EnvironmentConfigContextValue {
  setEnvironmentConfig: (config: EnvironmentConfig) => void;
}

const EnvironmentConfigContext = createContext<EnvironmentConfigContextValue | null>(null);

interface EnvironmentConfigProviderProps {
  children: ReactNode;
  setEnvironmentConfig: (config: EnvironmentConfig) => void;
}

export function EnvironmentConfigProvider({ 
  children, 
  setEnvironmentConfig 
}: EnvironmentConfigProviderProps) {
  return (
    <EnvironmentConfigContext.Provider value={{ setEnvironmentConfig }}>
      {children}
    </EnvironmentConfigContext.Provider>
  );
}

export function useEnvironmentConfig() {
  const context = useContext(EnvironmentConfigContext);
  if (!context) {
    throw new Error('useEnvironmentConfig must be used within EnvironmentConfigProvider');
  }
  return context;
}