import { router } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";
import Svg, { Defs, LinearGradient, Path, Stop } from "react-native-svg";
import { getColor } from "../(app)/V2/constants/colors";
import { useAuth } from "../AuthContext";
import { useDarkMode } from "../DarkModeContext";
import { discoverEnvironment, DiscoveryError, EnvironmentConfig, validateEmail } from "../services/discoveryService";

const HeroIcon = () => (
  <Svg width={42.667} height={48} viewBox="0 0 27 33" fill="none">
    <Path
      d="M11.707 0.279863C12.6445 -0.0932459 13.6915 -0.0933297 14.6289 0.279863L24.4941 4.2076C25.6063 4.65055 26.3349 5.71799 26.335 6.90486V13.3238C26.3349 19.9367 22.93 26.0935 17.3027 29.6558L13.8535 31.8394C13.4356 32.104 12.9004 32.1039 12.4824 31.8394L9.03223 29.6558C3.40493 26.0935 8.29607e-05 19.9367 0 13.3238V6.90486C7.00484e-05 5.71803 0.728737 4.65058 1.84082 4.2076L11.707 0.279863ZM22.8135 8.29451C22.8133 7.78109 22.2898 7.43343 21.8164 7.6324L15.2285 10.4019C13.9111 10.9557 12.4258 10.9557 11.1084 10.4019L4.52051 7.6324C4.04715 7.43343 3.52361 7.78109 3.52344 8.29451V13.3267C3.52358 18.8277 6.31642 23.9493 10.9316 26.9127L13.1689 28.3492L15.4053 26.9127C20.0205 23.9493 22.8133 18.8277 22.8135 13.3267V8.29451ZM13.0693 2.62068C11.6611 2.62069 10.5195 3.76227 10.5195 5.17049C10.5196 6.57867 11.6611 7.72028 13.0693 7.72029C14.4775 7.72029 15.6191 6.57867 15.6191 5.17049C15.6191 3.76226 14.4776 2.62068 13.0693 2.62068Z"
      fill="url(#paint0_linear_3216_34853)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_3216_34853"
        x1="13.1675"
        y1="32.0378"
        x2="13.1675"
        y2="0"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#0148BE" />
        <Stop offset="0.434672" stopColor="#005FDD" />
        <Stop offset="1" stopColor="#01B9FF" />
      </LinearGradient>
    </Defs>
  </Svg>
);

interface SignInScreenProps {
  onDiscoveryComplete: (config: EnvironmentConfig) => void;
  initialEmail?: string;
}

// Loading states for the discovery flow
enum FlowState {
  EMAIL_ENTRY = 'email_entry',
  DISCOVERING = 'discovering', 
  CONNECTING = 'connecting',
  ERROR = 'error'
}

/**
 * Extract organization display name from userPoolUrl
 */
function getOrganizationName(userPoolUrl: string): string {
  if (userPoolUrl.includes('auth.tcu.gethero.com')) {
    return 'TCU';
  }
  if (userPoolUrl.includes('auth.demo-1.gethero.com')) {
    return 'Demo Organization';
  }
  // Fallback: extract subdomain and format
  const match = userPoolUrl.match(/auth\.([^.]+)\./);
  return match ? `${match[1].charAt(0).toUpperCase() + match[1].slice(1)} Organization` : 'Organization';
}

export default function SignInScreen({ onDiscoveryComplete, initialEmail = "" }: SignInScreenProps) {
  const { isDarkMode } = useDarkMode();
  const { promptAsync, authTokens, clearEnvironmentConfig } = useAuth();
  
  const [email, setEmail] = useState(initialEmail);
  const [flowState, setFlowState] = useState<FlowState>(FlowState.EMAIL_ENTRY);
  const [organizationName, setOrganizationName] = useState<string>("");
  const [error, setError] = useState<string>("");
  
  // Track current discovery request for cancellation
  const discoveryAbortController = useRef<AbortController | null>(null);

  // Navigate to main app when authentication succeeds
  useEffect(() => {
    if (authTokens) {
      console.log("Authentication successful, navigating to main app");
      router.replace("/(app)/V2/MapScreen");
    }
  }, [authTokens]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (discoveryAbortController.current) {
        discoveryAbortController.current.abort();
      }
    };
  }, []);

  /**
   * Reset the flow back to email entry
   */
  const resetFlow = () => {
    // Cancel any ongoing discovery request
    if (discoveryAbortController.current) {
      discoveryAbortController.current.abort();
      discoveryAbortController.current = null;
    }
    
    setFlowState(FlowState.EMAIL_ENTRY);
    setError("");
    setOrganizationName("");
    setEmail(""); // Clear the email field
    
    // Clear environment config to prevent sticky state
    console.log("Clearing environment config for fresh start");
    clearEnvironmentConfig?.();
  };

  /**
   * Handle continue button press
   */
  const handleContinue = async () => {
    // Validate email first
    if (!validateEmail(email)) {
      setError("Please enter a valid email address");
      return;
    }

    // Clear previous error and start discovery
    setError("");
    setFlowState(FlowState.DISCOVERING);

    // Create new abort controller for this request
    discoveryAbortController.current = new AbortController();

    try {
      // Discover environment configuration
      const config = await discoverEnvironment(email, discoveryAbortController.current.signal);
      console.log("Discovery found config:", config);
      
      // Set organization name and transition to connecting state
      setOrganizationName(getOrganizationName(config.userPoolUrl));
      setFlowState(FlowState.CONNECTING);

      // Small delay to show the organization name, then attempt authentication
      setTimeout(async () => {
        try {
          // Complete discovery and trigger auth
          console.log("Calling onDiscoveryComplete with:", config);
          onDiscoveryComplete(config);
          
          // Wait a bit for AuthContext to process the new config
          await new Promise(resolve => setTimeout(resolve, 100));
          
          await promptAsync();
        } catch (authError) {
          console.error("Auth error:", authError);
          setError("Authentication failed. Please try again.");
          setFlowState(FlowState.ERROR);
        }
      }, 1000); // Small delay to show organization name

    } catch (error) {
      // Check if request was cancelled
      if (discoveryAbortController.current?.signal.aborted) {
        return;
      }

      console.error("Discovery error:", error);
      
      // Handle DiscoveryError with user-friendly messages
      if (error instanceof DiscoveryError) {
        setError(error.message);
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      setFlowState(FlowState.ERROR);
    } finally {
      discoveryAbortController.current = null;
    }
  };

  /**
   * Cancel the current discovery/auth flow
   */
  const handleCancel = () => {
    resetFlow();
  };

  /**
   * Render content based on current flow state
   */
  const renderContent = () => {
    switch (flowState) {
      case FlowState.EMAIL_ENTRY:
      case FlowState.ERROR:
        return (
          <>
            <Text
              style={[styles.title, { color: getColor("gray.900", isDarkMode) }]}
            >
              Welcome to Hero
            </Text>
            <Text
              style={[styles.subtitle, { color: getColor("gray.500", isDarkMode) }]}
            >
              Enter your email to get started
            </Text>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: getColor("gray.900", isDarkMode) }]}>
                Email
              </Text>
              <TextInput
                style={[
                  styles.emailInput,
                  {
                    backgroundColor: getColor("gray.100", isDarkMode), // Dark input background
                    borderColor: error ? "#ef4444" : getColor("blue.600", isDarkMode), // Blue border to match Figma
                    color: getColor("gray.900", isDarkMode), // Light text for dark mode
                  },
                ]}
                placeholder=""
                placeholderTextColor={getColor("gray.500", isDarkMode)}
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  if (error) setError(""); // Clear error on edit
                }}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
              
              {error && (
                <Text style={[styles.errorText, { color: "#ef4444" }]}>
                  {error}
                </Text>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.nextButton,
                { backgroundColor: getColor("blue.600", isDarkMode) },
              ]}
              onPress={handleContinue}
            >
              <Text style={styles.nextButtonText}>Next</Text>
            </TouchableOpacity>
          </>
        );

      case FlowState.DISCOVERING:
        return (
          <>
            <Text
              style={[styles.title, { color: getColor("gray.900", isDarkMode) }]}
            >
              Finding your organization...
            </Text>
            <Text
              style={[styles.subtitle, { color: getColor("gray.500", isDarkMode) }]}
            >
              {email}
            </Text>

            <View style={styles.loadingContainer}>
              <ActivityIndicator 
                size="large" 
                color={getColor("blue.600", isDarkMode)} 
              />
            </View>

            <TouchableOpacity
              style={[
                styles.cancelButton,
                { borderColor: getColor("gray.300", isDarkMode) },
              ]}
              onPress={handleCancel}
            >
              <Text style={[styles.cancelButtonText, { color: getColor("gray.700", isDarkMode) }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          </>
        );

      case FlowState.CONNECTING:
        return (
          <>
            <Text
              style={[styles.title, { color: getColor("gray.900", isDarkMode) }]}
            >
              Connecting to {organizationName}...
            </Text>
            <Text
              style={[styles.subtitle, { color: getColor("gray.600", isDarkMode) }]}
            >
              {email}
            </Text>

            <View style={styles.loadingContainer}>
              <ActivityIndicator 
                size="large" 
                color={getColor("blue.600", isDarkMode)} 
              />
            </View>

            <TouchableOpacity
              style={[
                styles.linkButton,
              ]}
              onPress={resetFlow}
            >
              <Text style={[styles.linkButtonText, { color: getColor("blue.600", isDarkMode) }]}>
                Use different email
              </Text>
            </TouchableOpacity>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: getColor("gray.50", isDarkMode) }, // Dark background for dark mode
      ]}
    >
      <View style={styles.content}>
        <View
          style={[
            styles.logoContainer,
            // Remove background color to match Figma - logo directly on dark background
          ]}
        >
          <HeroIcon />
        </View>

        {renderContent()}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24, // Spacing/M * 1.5 for side margins
    paddingTop: 80, // Top spacing instead of centering
    alignItems: "stretch", // Full width alignment
  },
  logoContainer: {
    alignSelf: "flex-start", // Left align the logo
    justifyContent: "center",
    alignItems: "center",
    width: 64, // Proper container size for logo
    height: 64,
    // Remove borderRadius and backgroundColor to match Figma
    marginBottom: 32, // Spacing/XL
  },
  title: {
    // Using Body/1 specifications from Figma: Roboto Medium 16px weight 500 lineHeight 140%
    fontFamily: "Roboto",
    fontSize: 24, // Larger for title
    fontWeight: "700", // Bold for title
    lineHeight: 33.6, // 24 * 1.4
    marginBottom: 8, // Spacing/XS
    textAlign: "left", // Left aligned
  },
  subtitle: {
    // Using Body/2 specifications from Figma: Roboto Regular 16px weight 400 lineHeight 140%
    fontFamily: "Roboto", 
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 22.4, // 16 * 1.4
    marginBottom: 32, // Spacing/XL
    textAlign: "left", // Left aligned
  },
  inputContainer: {
    width: "100%",
    marginBottom: 24, // Spacing/M + Spacing/XS
  },
  inputLabel: {
    // Using Body/4 specifications: Roboto Regular 14px weight 400
    fontFamily: "Roboto",
    fontSize: 14,
    fontWeight: "500", // Medium weight for label
    lineHeight: 19.6, // 14 * 1.4  
    marginBottom: 8, // Spacing/XS
    textAlign: "left",
  },
  emailInput: {
    width: "100%",
    height: 56, // Standard input height
    paddingHorizontal: 16, // Spacing/M
    paddingVertical: 16, // Spacing/M
    borderRadius: 12, // Spacing/S + Spacing/XXS
    borderWidth: 2, // Increased border width for prominence
    // Using Body/1 specifications for input text
    fontFamily: "Roboto",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 22.4, // 16 * 1.4
  },
  errorText: {
    // Using Body/4 for error text
    fontFamily: "Roboto",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 19.6, // 14 * 1.4
    marginTop: 8, // Spacing/XS
    textAlign: "left",
  },
  loadingContainer: {
    marginVertical: 32, // Spacing/XL
    alignItems: "center",
  },
  nextButton: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16, // Spacing/M
    paddingHorizontal: 24, // Spacing/M + Spacing/XS
    borderRadius: 12, // Spacing/S + Spacing/XXS
    width: "100%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nextButtonText: {
    // Using Body/1 specifications for button text
    fontFamily: "Roboto",
    color: "#fff",
    fontSize: 16,
    fontWeight: "500", // Medium weight
    lineHeight: 22.4, // 16 * 1.4
  },
  continueButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    width: "100%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  cancelButtonText: {
    fontFamily: "Roboto",
    fontSize: 16,
    fontWeight: "500",
    lineHeight: 22.4,
  },
  linkButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  linkButtonText: {
    fontFamily: "Roboto",
    fontSize: 16,
    fontWeight: "500",
    lineHeight: 22.4,
    textAlign: "center",
  },
  buttonIcon: {
    marginRight: 8,
  },
  continueButtonText: {
    color: "#fff",
    fontFamily: "Roboto",
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 25.2, // 18 * 1.4
  },
});