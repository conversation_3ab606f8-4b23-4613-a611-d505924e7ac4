import { MaterialCommunityIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Svg, { Defs, LinearGradient, Path, Stop } from "react-native-svg";
import { getColor } from "./(app)/V2/constants/colors";
import { useAuth } from "./AuthContext";
import { useDarkMode } from "./DarkModeContext";

const HeroIcon = () => (
  <Svg width={80} height={98} viewBox="0 0 27 33" fill="none">
    <Path
      d="M11.707 0.279863C12.6445 -0.0932459 13.6915 -0.0933297 14.6289 0.279863L24.4941 4.2076C25.6063 4.65055 26.3349 5.71799 26.335 6.90486V13.3238C26.3349 19.9367 22.93 26.0935 17.3027 29.6558L13.8535 31.8394C13.4356 32.104 12.9004 32.1039 12.4824 31.8394L9.03223 29.6558C3.40493 26.0935 8.29607e-05 19.9367 0 13.3238V6.90486C7.00484e-05 5.71803 0.728737 4.65058 1.84082 4.2076L11.707 0.279863ZM22.8135 8.29451C22.8133 7.78109 22.2898 7.43343 21.8164 7.6324L15.2285 10.4019C13.9111 10.9557 12.4258 10.9557 11.1084 10.4019L4.52051 7.6324C4.04715 7.43343 3.52361 7.78109 3.52344 8.29451V13.3267C3.52358 18.8277 6.31642 23.9493 10.9316 26.9127L13.1689 28.3492L15.4053 26.9127C20.0205 23.9493 22.8133 18.8277 22.8135 13.3267V8.29451ZM13.0693 2.62068C11.6611 2.62069 10.5195 3.76227 10.5195 5.17049C10.5196 6.57867 11.6611 7.72028 13.0693 7.72029C14.4775 7.72029 15.6191 6.57867 15.6191 5.17049C15.6191 3.76226 14.4776 2.62068 13.0693 2.62068Z"
      fill="url(#paint0_linear_3216_34853)"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear_3216_34853"
        x1="13.1675"
        y1="32.0378"
        x2="13.1675"
        y2="0"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#0148BE" />
        <Stop offset="0.434672" stopColor="#005FDD" />
        <Stop offset="1" stopColor="#01B9FF" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default function SignInScreen() {
  const { promptAsync, authTokens } = useAuth();
  const { isDarkMode } = useDarkMode();

  const onSignIn = async () => {
    const result = await promptAsync();
  };

  useEffect(() => {
    if (authTokens) {
      router.replace("/(app)/V2/MapScreen");
    }
  }, [authTokens]);

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: getColor("background", isDarkMode) },
      ]}
    >
      <View style={styles.content}>
        <View
          style={[
            styles.logoContainer,
            { backgroundColor: getColor("blue.50", isDarkMode) },
          ]}
        >
          <HeroIcon />
        </View>

        <Text
          style={[styles.title, { color: getColor("gray.900", isDarkMode) }]}
        >
          Welcome to Hero
        </Text>
        <Text
          style={[styles.subtitle, { color: getColor("gray.600", isDarkMode) }]}
        >
          Sign in to continue
        </Text>

        <TouchableOpacity
          style={[
            styles.signInButton,
            { backgroundColor: getColor("blue.600", isDarkMode) },
          ]}
          onPress={onSignIn}
        >
          <MaterialCommunityIcons
            name="shield-check"
            size={24}
            color="#fff"
            style={styles.buttonIcon}
          />
          <Text style={styles.signInButtonText}>Sign In</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24,
  },
  logoContainer: {
    width: 150,
    height: 150,
    borderRadius: 75,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 48,
    textAlign: "center",
  },
  signInButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    width: "100%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonIcon: {
    marginRight: 8,
  },
  signInButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
});
