import { Stack, StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";

import { Environment } from "../cloud-config/config";
import { servers } from "../cloud-config/default/servers";
import { ECRStack } from "./ecr-stack";
import { GithubActionsRoleStack } from "./github-actions-role-stack";
import { LambdasStack } from "./lambdas/lambdas-stack";
import { RootHostedZoneStack } from "./root-hosted-zone";

export interface EntryStackProps extends StackProps {
  pullAccountIds?: string[];
  environments?: Environment[];
}

export class EntryStack extends Stack {
  constructor(scope: Construct, id: string, props: EntryStackProps) {
    super(scope, id, props);

    // Extract account IDs from environmentInfos
    const pullAccountIds = props.environments?.map(env => env.accountId) || props.pullAccountIds;

    // create an ECR stack for each server
    for (const serverName of Object.keys(servers)) {
      new ECRStack(this, serverName, {
        pullAccountIds: pullAccountIds,
      });
    }

    // and some extras for the sensor base images
    new ECRStack(this, 'hero-core/sensors-build-base', {
      pullAccountIds: pullAccountIds,
    });

    new ECRStack(this, 'hero-core/sensors-runtime-base', {
      pullAccountIds: pullAccountIds,
    });

    new GithubActionsRoleStack(this, 'GithubActionsRoleStack', {
      githubOrg: 'herosafety',
      githubRepo: 'hero-core',
      githubBranch: 'main',
    });

    // create the root hosted zone
    new RootHostedZoneStack(this, 'RootHostedZone', {
      environments: props.environments,
    });

    // create shared lambdas (discovery, etc.)
    new LambdasStack(this, 'LambdasStack', {});
  }
}