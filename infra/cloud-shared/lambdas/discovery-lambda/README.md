# Discovery Lambda

This Lambda function provides email-to-environment discovery for mobile authentication.

## Purpose

Maps email domains to Cognito User Pool configurations, enabling a single mobile app build to authenticate across multiple environments (demo-1, TCU, etc.).

## API

**Endpoint**: Function URL (output from CDK deployment)

**Method**: POST

**Headers**: 
- `Content-Type: application/json`
- `X-Api-Key: <secret>` (required)

**Request**:
```json
{
  "email": "<EMAIL>"
}
```

**Response**:
```json
{
  "userPoolUrl": "https://auth.tcu.gethero.com",
  "clientId": "3fv7mur82c02q30o7oflj0u5ub"
}
```

**Error Response**:
```json
{
  "error": "No configuration found for domain: unknown.com"
}
```

## Domain Mapping

Currently hardcoded in `main.go`:
- `tcu.com` → TCU environment
- `gethero.com` → demo-1 environment
- `demo-1.gethero.com` → demo-1 environment

## Security

- API key validation (stored in AWS Secrets Manager)
- CORS configured for mobile app origins
- <PERSON><PERSON>atch logging for monitoring

## Building

```bash
# Build the Lambda
GOOS=linux GOARCH=amd64 go build -o bootstrap main.go
zip bootstrap.zip bootstrap

# Deploy via CDK
cd ../..
cdk deploy DiscoveryLambdaStack
```

## Testing

```bash
# Get the Function URL from CDK output
FUNCTION_URL="https://xyz.lambda-url.us-west-2.on.aws/"

# Get API key from Secrets Manager
API_KEY=$(aws secretsmanager get-secret-value --secret-id discovery-lambda/api-key --query SecretString --output text)

# Test the endpoint
curl -X POST $FUNCTION_URL \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: $API_KEY" \
  -d '{"email": "<EMAIL>"}'
```