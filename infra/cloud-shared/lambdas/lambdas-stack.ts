import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import * as path from 'path';

export interface LambdasStackProps extends cdk.StackProps {
}

export class LambdasStack extends cdk.Stack {
    public readonly discoveryFunctionUrl: string;
    
    constructor(scope: Construct, id: string, props: LambdasStackProps) {
        super(scope, id, props);

        // Create API key secret for discovery service
        const discoveryApiKeySecret = new secretsmanager.Secret(this, 'DiscoveryApiKeySecret', {
            secretName: 'shared/discovery-lambda/api-key',
            generateSecretString: {
                passwordLength: 32,
                excludePunctuation: true,
                excludeUppercase: false,
                excludeLowercase: false,
                excludeNumbers: false,
            },
        });

        // Discovery Lambda for mobile multi-tenant auth
        const discoveryLambda = new lambda.Function(this, 'DiscoveryLambda', {
            functionName: 'shared-discovery-lambda',
            runtime: lambda.Runtime.PROVIDED_AL2023,
            handler: 'main',
            timeout: cdk.Duration.seconds(10),
            code: lambda.Code.fromAsset(path.join(__dirname, 'discovery-lambda/bootstrap.zip')),
            environment: {
                API_KEY_SECRET_ARN: discoveryApiKeySecret.secretArn,
                PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL: 'debug',
                PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE: '10',
                SECRETS_MANAGER_TTL: '300',
            },
            paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
                lambda.ParamsAndSecretsVersions.V1_0_103,
                {
                    cacheSize: 10,
                    logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
                }
            ),
        });

        // Grant Lambda permission to read the API key secret
        discoveryApiKeySecret.grantRead(discoveryLambda);

        // Create Function URL for mobile app access
        const functionUrl = discoveryLambda.addFunctionUrl({
            authType: lambda.FunctionUrlAuthType.NONE,
            cors: {
                allowedOrigins: ['*'], // Restrict this in production if needed
                allowedMethods: [lambda.HttpMethod.POST],
                allowedHeaders: ['Content-Type', 'X-Api-Key'],
                maxAge: cdk.Duration.hours(1),
            },
        });

        this.discoveryFunctionUrl = functionUrl.url;

        // Outputs for easy access
        new cdk.CfnOutput(this, 'DiscoveryFunctionUrl', {
            value: this.discoveryFunctionUrl,
            description: 'Discovery Lambda Function URL for mobile auth',
            exportName: 'SharedDiscoveryFunctionUrl',
        });

        new cdk.CfnOutput(this, 'DiscoveryApiKeySecretArn', {
            value: discoveryApiKeySecret.secretArn,
            description: 'Discovery API Key Secret ARN',
            exportName: 'SharedDiscoveryApiKeySecretArn',
        });
    }
}