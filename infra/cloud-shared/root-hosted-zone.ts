import * as cdk from 'aws-cdk-lib';
import { AccountPrincipal, PolicyDocument, PolicyStatement, Role } from 'aws-cdk-lib/aws-iam';
import { PublicHostedZone } from 'aws-cdk-lib/aws-route53';
import { Construct } from 'constructs';
import { Environment } from '../cloud-config/config';

export interface RootHostedZoneStackProps extends cdk.StackProps {
    environments?: Environment[];
}

export class RootHostedZoneStack extends cdk.Stack {
    private hostedZone: PublicHostedZone;

    constructor(scope: Construct, id: string, props?: RootHostedZoneStackProps) {
        super(scope, id, props);

        // create the root hosted zone
        this.hostedZone = new PublicHostedZone(this, 'HostedZone', {
            zoneName: 'gethero.com',
        });

        // create a delegation role for each environment, 
        // which allows the environment subaccounts to manage their subdomains
        if (props?.environments?.length) {
            for (const environment of props.environments) {
                this.createDelegationRole({
                    accountId: environment.accountId,
                    roleName: environment.envName + '-HostedZoneDelegationRole',
                    allowedSubdomain: environment.domain,
                });
            }
        }
    }

    private createDelegationRole(params: {
        accountId: string;
        roleName: string;
        allowedSubdomain: string;
    }): Role {
        const { accountId, roleName, allowedSubdomain } = params;

        // Validate this can only happen for the specific sub-domain
        const conditions = {
            'ForAllValues:StringEquals': {
                'route53:ChangeResourceRecordSetsRecordTypes': ['NS'],
                'route53:ChangeResourceRecordSetsActions': ['UPSERT', 'DELETE'],
                'route53:ChangeResourceRecordSetsNormalizedRecordNames': [allowedSubdomain]
            }
        };

        // Allow the role to perform the GetHostedZone and ChangeResourceRecordSets methods on the recordset for the subdomain.
        const recordSetPolicyStatement = new PolicyStatement({
            actions: ["route53:GetHostedZone", "route53:ChangeResourceRecordSets"],
            resources: [this.hostedZone.hostedZoneArn],
            conditions: conditions
        });

        // Allow the role to list hosted zones by name
        const zoneListingPolicyStatement = new PolicyStatement({
            actions: ["route53:ListHostedZonesByName"],
            resources: ["*"]
        });

        const policyDocument = new PolicyDocument({
            statements: [recordSetPolicyStatement, zoneListingPolicyStatement]
        });

        const role = new Role(this, `ParentZoneAccountRole-${roleName}`, {
            assumedBy: new AccountPrincipal(accountId),
            roleName: roleName,
            inlinePolicies: {
                delegation: policyDocument,
            },
        });

        this.hostedZone.grantDelegation(role);

        return role;
    }
}