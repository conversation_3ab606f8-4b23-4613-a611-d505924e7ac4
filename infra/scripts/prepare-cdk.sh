#!/bin/bash
set -e

# install cdk dependencies
npm install -g aws-cdk
cd infra
npm i 
cd -

########### build the lambda functions ############
cd infra
cd cloud/apps/authorizationLambda/
npm i
npm run build
cd - 

cd cloud/shared/cognito-stack/postConfirmationLambda/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd - 

cd cloud/shared/cognito-stack/preSignUpLambda/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd - 

cd cloud/lambdas/camera-listener/sns-to-situation/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd -

cd cloud/lambdas/camera-listener/sns-to-db/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd -

cd cloud/lambdas/camera-listener/sns-to-s3/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd -

cd cloud/servers/lambda/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd -

cd cloud/lambdas/twilio-monitor/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap || exit 1
cd -

cd cloud/lambdas/migration-runner/
mkdir -p migrations
cp ../../../../migrations/*.sql migrations/
GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap main.go || exit 1
zip bootstrap.zip bootstrap
rm -rf migrations
cd -

cd ..
####################################################
