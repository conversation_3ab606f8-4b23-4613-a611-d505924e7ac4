# Hero Sentry SDK

A simplified Sentry integration SDK for Hero Core services that automatically captures rich context and enables distributed tracing across microservices.

## Table of Contents

- [Features](#features)
- [Traces vs Spans: Quick Primer](#traces-vs-spans-quick-primer)
- [Quick Start](#quick-start)
  - [First Time Setup](#first-time-setup-one-time-configuration)
  - [Daily Use](#daily-use-what-you-do-every-day)
- [What Gets Auto-Captured](#what-gets-auto-captured)
- [API Reference](#api-reference)
  - [Initialization Functions](#initialization-functions)
  - [Span Functions](#span-functions)
  - [Error Handling](#error-handling)
  - [Interceptors](#interceptors)
- [Searching in Sentry](#searching-in-sentry)
  - [Finding Your Traces](#finding-your-traces)
  - [Narrowing Down Issues](#narrowing-down-issues)
  - [Example: Debugging a Failed CreateSituation](#example-debugging-a-failed-createsituation)
- [Distributed Tracing](#distributed-tracing)
  - [How It Works](#how-it-works)
  - [Visual Example](#visual-example)
- [Configuration](#configuration)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Features

- **Automatic Context Capture**: Automatically captures user, organization, IP address, request ID, and code location
- **Distributed Tracing**: Seamlessly propagates traces across service boundaries
- **Zero Configuration**: Smart defaults based on environment
- **Clean API**: Only 8 public functions to learn
- **Type-Safe**: Idiomatic Go with clear interfaces

## Traces vs Spans: Quick Primer

**Trace**: The entire journey of a request across all services
- Think of it as a "story" of what happened from start to finish
- Has a unique trace ID that follows the request everywhere
- Contains multiple spans from different services

**Span**: A single operation within that journey
- Think of it as a "chapter" in the trace story
- Has timing info (start/end), tags, and custom data
- Can have child spans (sub-operations)

**Example**: 
- **Trace**: "A situation status change creates an order and notify the user" (the full story)
  - **Span 1**: "SituationService.UpdateStatus" (one chapter)
  - **Span 2**: "OrderService.CreateOrder" (another chapter)
    - **Span 1.1**: "validate order" (sub-chapter)
    - **Span 1.2**: "check asset eligibility to do the order" (sub-chapter)
    - **Span 1.3**: "save to database" (sub-chapter)
  - **Span 3** Not Implemented notification service doing some work
 

**With herosentry**: Interceptors create traces automatically. You just create spans!

## Quick Start

### First Time Setup (One-time Configuration)

If your service has no setup for sentry these are the steps you need to follow to set it up so that it can actually send the data to sentry. 

#### 1. Import the SDK

The SDK is already available in the Hero Core monorepo:

```go
import "common/herosentry"
```

#### 2. Set Environment Variables

Make sure these following environment variables are somehow injected into your service 

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `SENTRY_DSN` | Yes* | None | Sentry project DSN. If not set, SDK runs in no-op mode |
| `ENVIRONMENT` | No | `"development"` | Deployment environment (e.g., `prod-1`, `demo-1`, `staging`) |
| `APP_VERSION` | No | None | Application version for release tracking |

*Without `SENTRY_DSN`, the service will have no observability.

#### 3. Initialize in main.go

```go
func main() {
    // Initialize Sentry with automatic configuration
    if err := herosentry.Init("workflow-service"); err != nil {
        log.Printf("Failed to initialize Sentry: %v", err)
    }
    defer herosentry.Flush() // Ensure all events are sent before shutdown
    
    // Your service setup...
}
```

#### Default Initialization Values

When using `herosentry.Init("service-name")` without custom config, these defaults apply:

| Setting | Default Value | Description |
|---------|---------------|-------------|
| **DSN** | `SENTRY_DSN` env var | Data Source Name for Sentry project |
| **Environment** | `ENVIRONMENT` env var or `"development"` | Deployment environment |
| **Sample Rate (dev)** | `100%` (1.0) | All transactions captured in development |
| **Sample Rate (prod)** | `10%` (0.1) | 1 in 10 transactions captured in production |
| **Release** | `APP_VERSION` env var or none | Version tracking |
| **Send PII** | `false` | No personally identifiable information sent |
| **Enable Tracing** | `true` | Distributed tracing enabled |
| **Flush Timeout** | `2 seconds` | Time to wait for events on shutdown |

### No-Op Mode

If `SENTRY_DSN` is not set, the SDK will:
- Print a warning: `WARNING: SENTRY_DSN not set. Herosentry will operate in no-op mode.`
- Continue running without errors
- Make all operations no-ops (no data sent to Sentry)

This allows services to run in development or testing without Sentry configuration.


#### 4. Add Interceptors to Your Routes

Interceptors automatically handle distributed tracing without any manual work. Choose based on your endpoint type:

#### For RPC Services (Incoming Requests)
```go
// Add to your service routes to automatically:
// - Create root transactions for new requests
// - Continue traces from upstream services
// - Capture service/method names, errors, and duration
// - Extract user context from JWT tokens

mux.Handle(orderPath,
    connect.NewHandler(
        orderService,
        connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
    ),
)
```

#### For RPC Clients (Outgoing Calls)
```go
// Add to your service clients to automatically:
// - Create child spans for outgoing calls
// - Propagate trace headers to downstream services
// - Track external service performance
// - Capture failures and latency

assetClient := asset.NewClient(
    httpClient,
    url,
    connect.WithInterceptors(herosentry.RPCClientInterceptor()),
)

// Now all calls through this client are automatically traced!
asset, err := assetClient.GetAsset(ctx, &GetAssetRequest{Id: "123"})
```

#### For HTTP Endpoints (Webhooks, Health Checks)
```go
// Add to HTTP handlers to automatically:
// - Create transactions for HTTP requests
// - Capture method, path, and status codes
// - Handle panics gracefully
// - Extract client IP addresses

mux.Handle("/webhook/twilio",
    herosentry.HTTPMiddleware(
        http.HandlerFunc(handleTwilioWebhook),
    ),
)

mux.Handle("/health",
    herosentry.HTTPMiddleware(
        http.HandlerFunc(healthCheck),
    ),
)
```

### Daily Use (What You Do Every Day)

Once your service has the setup, these are the steps to capture any peace of event happening in your code. 

#### 1. Track Operations with Spans

```go
// In your business logic, just add spans where you want visibility
func CreateOrder(ctx context.Context, req *CreateOrderRequest) (*Order, error) {
    // Track this operation
    ctx, span, finish := herosentry.StartSpan(ctx, "OrderUseCase.CreateOrder")
    defer finish()
    
    // Add business context
    span.SetTag("order.type", req.Type)
    span.SetTag("order.priority", req.Priority)
    
    // Your code...
    return order, nil
}
```

#### 2. Capture Errors with Context

```go
if err := validateOrder(req); err != nil {
    // Automatically includes user, org, request ID, stack trace
    herosentry.CaptureException(ctx, err, "Order validation failed")
    return nil, err
}
```

#### 3. Add Custom Tags and Data

```go
// Tags are searchable strings
span.SetTag("payment.method", "stripe")
span.SetTag("feature.flag", "new-checkout")

// Data can be complex objects
span.SetData("order.items", orderItems)
span.SetData("validation.errors", validationResult)
```

That's it! The interceptors handle all the complex distributed tracing automatically.

## What Gets Auto-Captured

### User Context
- **Username**: From JWT token (e.g., "cognito:abc123" or "bot:service-name")
- **Organization ID**: From JWT claims
- **IP Address**: From request headers (X-Forwarded-For, etc.)

### Code Location
- **File**: Relative path to the file
- **Line**: Line number where span was created
- **Function**: Function name

### Request Context
- **Request ID**: From X-Request-ID or similar headers
- **Environment**: From ENVIRONMENT env var
- **Service Name**: From Init() call

### RPC Endpoints (Automatically Captured)
- **Service Name**: Extracted from procedure (e.g., "OrderService")
- **Method Name**: Extracted from procedure (e.g., "CreateOrder")
- **Transaction Name**: Combined as "OrderService.CreateOrder"
- **RPC Type**: "server" for incoming, "client" for outgoing
- **Error Status**: Automatically set on failures

### HTTP Endpoints (Automatically Captured)
- **HTTP Method**: GET, POST, PUT, DELETE, etc.
- **HTTP Path**: The request path (e.g., "/webhook/twilio")
- **Transaction Name**: Combined as "POST /webhook/twilio"
- **Status Code**: HTTP response status
- **Full URL**: Complete request URL

### Example Sentry Output

```
Transaction: OrderService.CreateOrder
├─ trace.id: d4cda95b652f4a1592b449d5929fda1b
├─ span.id: 6e0c63257de34c92
├─ user.username: cognito:abc123-def456
├─ org.id: 42
├─ user.ip_address: *************
├─ code.file: services/workflow/internal/orders/usecase.go
├─ code.line: 45
├─ code.function: (*OrderUsecase).CreateOrder
├─ environment: production
├─ service: workflow-service
├─ rpc.service: OrderService
├─ rpc.method: CreateOrder
├─ rpc.type: server
├─ span.kind: server
├─ order.type: express (custom tag)
├─ order.amount: 99.99 (custom data)
└─ duration: 234ms
```

## API Reference

The herosentry SDK provides only 8 public functions - everything else is handled automatically!

### Initialization Functions

#### `Init(serviceName string, config ...Config) error`
Initializes Sentry for the service. Call once in main().

```go
// Basic initialization
err := herosentry.Init("my-service")

// With custom configuration
err := herosentry.Init("my-service", herosentry.Config{
    Environment:           "staging",
    DevelopmentSampleRate: &customDevRate,
    ProductionSampleRate:  &customProdRate,
})
```

#### `Flush()`
Flushes all pending events to Sentry. Call before shutdown.

```go
defer herosentry.Flush()  // Always call before shutdown
```

### Span Functions

#### `StartSpan(ctx context.Context, operationName string) (context.Context, Span, FinishFunc)`
Starts a new span. Automatically creates child spans or transactions as needed. Returns the span for immediate use.

```go
ctx, span, finish := herosentry.StartSpan(ctx, "database.query")
defer finish()
span.SetTag("db.table", "orders")
```

#### `CurrentSpan(ctx context.Context) Span`
Gets the current span to add custom data without creating a new one.

```go
if span := herosentry.CurrentSpan(ctx); span != nil {
    span.SetTag("db.table", "orders")
    span.SetData("query", sqlQuery)
}
```

### Error Handling

#### `CaptureException(ctx context.Context, err error, message ...string)`
Captures an error with full context. Optionally accepts a custom message.

```go
// Basic usage
if err := db.Query(ctx, query); err != nil {
    herosentry.CaptureException(ctx, err)
    return err
}

// With custom message
herosentry.CaptureException(ctx, err, "Failed to process payment")
```

### Interceptors

#### `RPCServiceInterceptor() connect.Interceptor`
For incoming RPC requests (server-side). This interceptor:
- Creates root transactions for new requests or continues existing traces
- Automatically names transactions as "ServiceName.MethodName"
- Captures all errors and panics with full context
- Extracts user/org context from JWT tokens
- Sets `rpc.service`, `rpc.method`, `rpc.type: "server"` tags

```go
mux.Handle(orderPath,
    connect.NewHandler(
        orderService,
        connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
    ),
)
```

#### `RPCClientInterceptor() connect.Interceptor`
For outgoing RPC calls (client-side). This interceptor:
- Creates child spans named "calling ServiceName.MethodName"
- Propagates trace context via `sentry-trace` header
- Tracks call duration and success/failure
- Sets `rpc.service`, `rpc.method`, `rpc.type: "client"` tags
- Enables distributed tracing across services

```go
client := asset.NewClient(
    httpClient,
    url,
    connect.WithInterceptors(herosentry.RPCClientInterceptor()),
)
```

#### `HTTPMiddleware(handler http.Handler) http.Handler`
For HTTP endpoints (webhooks, health checks, etc.). This middleware:
- Creates transactions named "METHOD /path"
- Captures HTTP method, path, status code, and URL
- Handles panics and returns 500 status
- Extracts client IP from headers
- Sets `http.method`, `http.path`, `http.status_code` tags

```go
http.Handle("/webhook/twilio", 
    herosentry.HTTPMiddleware(webhookHandler),
)
```

## Searching in Sentry

### Finding Your Traces

When debugging a specific operation like `CreateSituation`, here's how to find it in Sentry:

#### 1. Search by Transaction Name
```
transaction:SituationService.CreateSituation
```

#### 2. Search by Service
```
service:workflow-service
```

#### 3. Search by User
```
user.username:cognito:abc123-def456
```

#### 4. Search by Custom Tags
```
situation.type:INCIDENT
situation.priority:1
```

#### 5. Search by Error
```
error:true
```

#### 6. Combine Filters
```
transaction:*CreateSituation* environment:production error:true
```

### Narrowing Down Issues

1. **Time Range**: Use the time picker to focus on when the issue occurred
2. **Filter by Environment**: `environment:production` vs `environment:development`
3. **Filter by Organization**: `org.id:42`
4. **Trace View**: Click on a transaction to see the full distributed trace
5. **Breadcrumbs**: Check the timeline of events leading to an error

### Example: Debugging a Failed CreateSituation

1. Go to Sentry → Performance → Transactions
2. Search: `transaction:SituationService.CreateSituation error:true`
3. Click on a failed transaction
4. View the trace to see:
   - Which step failed (validation, database, external service?)
   - User context (who triggered it?)
   - Custom data (what situation type?)
   - Full stack trace if an exception occurred

## Distributed Tracing

### How It Works

1. **Interceptors create traces** - When a request arrives, the interceptor creates a new trace or continues an existing one
2. **You create spans** - Use `StartSpan()` to track operations within your service
3. **SDK handles propagation** - Trace context automatically flows to downstream services

### Visual Example

```
User Request → Workflow Service
              │
              ├─ [Transaction] OrderService.CreateOrder  ← Created by RPCServiceInterceptor
              │   ├─ [Span] order.validate               ← You create this with StartSpan
              │   ├─ [Span] calling AssetService.GetAsset ← Created by RPCClientInterceptor
              │   │                                          │
              │   │                                          └─ [Transaction] AssetService.GetAsset
              │   │                                              └─ [Span] database.query
              │   └─ [Span] database.insert              ← You create this with StartSpan
              │
              └─ Response to User
```

**Key Point**: You never manage traces directly. Just add spans where you want visibility!

## Configuration

### Automatic Sampling Rates

The SDK automatically adjusts sampling rates based on the `ENVIRONMENT` variable:

| Environment | Sample Rate | Description |
|-------------|-------------|-------------|
| `development` | 100% | All transactions captured for debugging |
| Any other value | 10% | Reduced sampling for production environments |

### Custom Configuration

You can override defaults using the optional `Config` parameter:

```go
// Custom sampling rates
devRate := 0.5    // 50% in development
prodRate := 0.05  // 5% in production

if err := herosentry.Init("my-service", herosentry.Config{
    DataSourceName:        "https://<EMAIL>/123",
    Environment:           "staging",
    DevelopmentSampleRate: &devRate,
    ProductionSampleRate:  &prodRate,
    Release:               "v2.0.0",
}); err != nil {
    log.Printf("Failed to initialize Sentry: %v", err)
}
```

## Best Practices

1. **Use descriptive operation names**: `"OrderUseCase.CreateOrder"` instead of `"create"`
2. **Add business context**: Use SetTag for searchable strings, SetData for complex objects
3. **Let auto-capture work**: Don't manually set user/org/IP - the SDK does this
4. **One span per operation**: Don't create spans for every function call
5. **Handle errors consistently**: Always use CaptureException for errors

## Troubleshooting

### Events not appearing in Sentry?
1. Check `SENTRY_DSN` is set correctly
2. Ensure `herosentry.Init()` is called before any operations
3. Call `herosentry.Flush()` before service shutdown
4. Check service logs for initialization errors

### Missing context data?
1. Ensure authentication middleware runs before your handlers
2. Check that context is properly propagated through your call chain
3. Use `CurrentSpan(ctx)` to verify span exists before adding data

### Traces not connecting across services?
1. Ensure both services use the interceptors
2. Check that services can reach each other
3. Verify trace headers aren't being stripped by proxies