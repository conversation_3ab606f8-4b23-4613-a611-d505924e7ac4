// Package herosentry provides context capture functionality for the Hero Sentry SDK.
// This file contains functions for automatically capturing runtime context,
// authentication information, and request metadata to enrich spans and errors.
//
// The capture system extracts information from multiple sources:
// - Runtime stack for code location
// - Hero context for user and organization data
// - HTTP headers for request correlation
// - Environment variables for deployment context
//
// All captured data is automatically added to spans and errors to provide
// rich debugging information in Sentry.
package herosentry

import (
	cmncontext "common/context"
	"context"
	"fmt"
	"net/http"
	"os"
	"runtime"
	"strconv"
	"strings"

	"github.com/getsentry/sentry-go"
)

// captureRuntimeInfo captures file, line, and function information from the caller.
// This provides code location context for debugging in Sentry.
//
// Parameters:
//   - skipFrames: Number of stack frames to skip (usually 2 for the capture function and its caller)
//
// Returns:
//   - fileName: Source file path, relative to working directory if possible
//   - lineNumber: Line number in the source file
//   - functionName: Function name, cleaned for readability
//
// Example output:
//
//	fileName: "services/workflow/internal/usecase.go"
//	lineNumber: 42
//	functionName: "(*OrderUsecase).CreateOrder"
//
// The function handles various edge cases:
// - Returns "unknown" values if runtime information is unavailable
// - Cleans up function names to remove package paths
// - Makes file paths relative to improve readability
func captureRuntimeInfo(skipFrames int) (fileName string, lineNumber int, functionName string) {
	// Get caller information from runtime
	programCounter, fileName, lineNumber, success := runtime.Caller(skipFrames)
	if !success {
		// Unable to get runtime info, return defaults
		return "unknown", 0, "unknown"
	}

	// Extract function name from program counter
	function := runtime.FuncForPC(programCounter)
	if function != nil {
		functionName = function.Name()

		// Clean up function name for readability
		// Original: "github.com/company/hero-core/services/workflow/internal.(*OrderUsecase).CreateOrder"
		// After cleanup: "(*OrderUsecase).CreateOrder"

		// Remove package path (everything before last slash)
		if lastSlashIndex := strings.LastIndex(functionName, "/"); lastSlashIndex >= 0 {
			functionName = functionName[lastSlashIndex+1:]
		}

		// Remove remaining package name before first dot
		if dotIndex := strings.Index(functionName, "."); dotIndex >= 0 {
			functionName = functionName[dotIndex+1:]
		}
	}

	// Make file path relative to working directory for better readability
	// This converts absolute paths to relative ones in Sentry UI
	if workingDirectory, workingDirError := os.Getwd(); workingDirError == nil {
		if strings.HasPrefix(fileName, workingDirectory) {
			// Remove working directory prefix
			fileName = strings.TrimPrefix(fileName, workingDirectory)
			// Remove leading slash
			fileName = strings.TrimPrefix(fileName, "/")
		}
	}

	return fileName, lineNumber, functionName
}

// captureAuthContext extracts authentication context from the Hero context.
// This captures user identity and organization information automatically.
//
// Parameters:
//   - context: The context containing Hero authentication data
//
// Returns:
//   - username: User identifier (e.g., "cognito:abc123", "bot:worker-service")
//   - organizationID: Organization ID from JWT claims
//   - ipAddress: Client IP address from request headers
//
// Example outputs:
//
//	username: "cognito:550e8400-e29b-41d4-a716-446655440000"
//	organizationID: 42
//	ipAddress: "*************"
//
// The function uses Hero's context helpers which extract data from:
// - JWT tokens in Authorization headers
// - X-Forwarded-For headers for IP addresses
// - Custom Hero headers for service-to-service calls
func captureAuthContext(context context.Context) (username string, organizationID int32, ipAddress string) {
	// Extract username from context
	// Format: "cognito:sub" for users, "bot:service-name" for services
	username = cmncontext.GetUsername(context)

	// Extract organization ID from JWT claims
	organizationID = cmncontext.GetOrgId(context)

	// Extract client IP address from headers
	// Checks X-Forwarded-For, X-Real-IP, etc.
	ipAddress = cmncontext.GetIPAddress(context)

	return username, organizationID, ipAddress
}

// captureRequestContext extracts request-related information from context.
// This captures request correlation IDs for tracing requests across systems.
//
// Parameters:
//   - context: The context potentially containing HTTP request data
//
// Returns:
//   - requestID: Request correlation ID from headers, or empty string
//
// The function checks common header names used by various systems:
// - X-Request-ID: Standard request ID header
// - X-Amzn-Request-Id: AWS API Gateway request ID
// - X-Correlation-ID: Alternative correlation header
//
// Example output:
//
//	requestID: "550e8400-e29b-41d4-a716-446655440000"
func captureRequestContext(context context.Context) (requestID string) {
	// Extract HTTP request from context if available
	if httpRequest, isHTTPRequest := context.Value(httpRequestContextKey).(*http.Request); isHTTPRequest {
		// Check common request ID headers in order of preference

		// Standard request ID header
		requestID = httpRequest.Header.Get("X-Request-ID")
		if requestID == "" {
			// AWS API Gateway request ID
			requestID = httpRequest.Header.Get("X-Amzn-Request-Id")
		}
		if requestID == "" {
			// Alternative correlation header
			requestID = httpRequest.Header.Get("X-Correlation-ID")
		}
	}

	return requestID
}

// captureEnvironment gets the environment from environment variable.
// This determines the deployment environment for filtering in Sentry.
//
// Returns:
//   - string: Environment name ("demo-1", "prod-1", "development")
//
// The function reads the ENVIRONMENT variable and defaults to "development"
// if not set. This matches the behavior in the Init() function.
//
// Example outputs:
//   - "prod-1" (when ENVIRONMENT=prod-1)
//   - "development" (when ENVIRONMENT is not set)
func captureEnvironment() string {
	environment := os.Getenv("ENVIRONMENT")
	if environment == "" {
		// Default to development for safety
		return "development"
	}
	return environment
}

// enrichSpanWithContext adds all auto-captured context to a span.
// This is the main function that populates spans with rich debugging information.
//
// Parameters:
//   - span: The internal span to enrich
//   - context: The context containing authentication and request data
//   - skipFrames: Stack frames to skip for accurate code location
//
// The function captures and adds:
// - Code location (file, line, function)
// - User context (username, org ID, IP)
// - Request context (request ID)
// - Environment information
// - Service name
//
// All data is added as tags for searchability in Sentry.
//
// Example tags added:
//
//	"code.file": "services/workflow/internal/usecase.go"
//	"code.line": "42"
//	"code.function": "(*OrderUsecase).CreateOrder"
//	"user.username": "cognito:abc123"
//	"user.type": "cognito"
//	"org.id": "42"
//	"user.ip_address": "*************"
//	"request.id": "550e8400-e29b-41d4-a716"
//	"environment": "production"
//	"service": "workflow-service"
func enrichSpanWithContext(span *internalSpan, context context.Context, skipFrames int) {
	// Capture all context information
	// Each capture function is designed to never fail

	// 1. Code location for debugging
	span.file, span.line, span.function = captureRuntimeInfo(skipFrames)

	// 2. Authentication and authorization context
	span.username, span.orgID, span.ipAddress = captureAuthContext(context)

	// 3. Request correlation information
	span.requestID = captureRequestContext(context)

	// 4. Deployment environment
	span.environment = captureEnvironment()

	// Add all captured data as searchable tags
	if span.Span != nil {
		// Code location tags for finding where spans originate
		span.SetTag("code.file", span.file)
		span.SetTag("code.line", strconv.Itoa(span.line))
		span.SetTag("code.function", span.function)

		// User authentication tags
		if span.username != "" {
			span.SetTag("user.username", span.username)

			// Parse username format to determine user type
			// This helps filter by user type in Sentry
			if strings.HasPrefix(span.username, "cognito:") {
				// Cognito authenticated user
				span.SetTag("user.type", "cognito")
				span.SetTag("user.cognito_id", strings.TrimPrefix(span.username, "cognito:"))
			} else if strings.HasPrefix(span.username, "bot:") {
				// Service-to-service authentication
				span.SetTag("user.type", "bot")
				span.SetTag("user.bot_name", strings.TrimPrefix(span.username, "bot:"))
			} else {
				// API key or other authentication
				span.SetTag("user.type", "api")
			}
		}

		// Organization context for multi-tenant filtering
		if span.orgID > 0 {
			span.SetTag("org.id", strconv.Itoa(int(span.orgID)))
		}

		// IP address for security and debugging
		if span.ipAddress != "" {
			span.SetTag("user.ip_address", span.ipAddress)
		}

		// Request correlation for distributed debugging
		if span.requestID != "" {
			span.SetTag("request.id", span.requestID)
		}

		// Environment tag (also set globally, but included for consistency)
		span.SetTag("environment", span.environment)

		// Service identifier for filtering by service
		span.SetTag("service", getServiceName())
	}
}

// CaptureException sends an error to Sentry with full context and an optional message.
// This is the primary method for reporting errors in Hero services.
//
// The function automatically captures:
// - Error type and message
// - Stack trace
// - User context (username, org, IP)
// - Code location where error was reported
// - Request correlation ID
// - Environment and service information
//
// Parameters:
//   - context: The context containing authentication and request data
//   - errorToCapture: The error to report to Sentry
//   - message: Optional user-generated message for additional context (variadic)
//
// The function is safe to call with nil errors or when Sentry is not initialized.
// In these cases, it's a no-op.
//
// Example usage:
//
//	// Without message
//	order, err := db.GetOrder(ctx, orderID)
//	if err != nil {
//	    herosentry.CaptureException(ctx, err)
//	    return nil, fmt.Errorf("failed to get order: %w", err)
//	}
//
//	// With message for additional context
//	if err := processPayment(ctx, payment); err != nil {
//	    herosentry.CaptureException(ctx, err, "Payment processing failed for order "+orderID)
//	    return nil, err
//	}
//
// Example Sentry output:
//
//	Message: Payment processing failed for order ORD-12345
//	Error: pq: relation "orders" does not exist
//	Type: *pq.Error
//
//	Tags:
//	  error.file: services/workflow/internal/repo.go
//	  error.line: 145
//	  error.function: (*OrderRepo).GetByID
//	  user.username: cognito:abc123
//	  org.id: 42
//	  request.id: 550e8400-e29b-41d4
//	  error.type: *pq.Error
//
//	User:
//	  Username: cognito:abc123
//	  IP: *************
//
// Best practices:
// - Always capture errors at the point they occur
// - Include context about what operation failed
// - Don't capture expected errors (e.g., validation errors)
// - Use alongside proper error handling and logging
func CaptureException(context context.Context, errorToCapture error, message ...string) {
	// Skip if no error or Sentry not initialized
	if errorToCapture == nil || !isInitialized() {
		return
	}

	// Get Sentry hub from context or use global hub
	sentryHub := sentry.GetHubFromContext(context)
	if sentryHub == nil {
		sentryHub = sentry.CurrentHub()
	}

	// Clone hub to avoid modifying shared state
	sentryHub = sentryHub.Clone()

	// Configure scope with all context information
	sentryHub.WithScope(func(scope *sentry.Scope) {
		// 1. Set user context for the error
		username, organizationID, ipAddress := captureAuthContext(context)
		if username != "" {
			// Set user in Sentry's user context
			scope.SetUser(sentry.User{
				Username:  username,
				IPAddress: ipAddress,
			})
			// Also add as tag for searching
			scope.SetTag("user.username", username)
		}

		// 2. Add organization context
		if organizationID > 0 {
			scope.SetTag("org.id", strconv.Itoa(int(organizationID)))
		}

		// 3. Capture where the error was reported from
		// Skip 4 frames: captureRuntimeInfo, closure, WithScope, and CaptureException
		fileName, lineNumber, functionName := captureRuntimeInfo(4)
		scope.SetTag("error.file", fileName)
		scope.SetTag("error.line", strconv.Itoa(lineNumber))
		scope.SetTag("error.function", functionName)

		// 4. Add request correlation
		if requestID := captureRequestContext(context); requestID != "" {
			scope.SetTag("request.id", requestID)
		}

		// 5. Add error type for filtering
		// Example: "*pq.Error", "*errors.errorString"
		scope.SetTag("error.type", fmt.Sprintf("%T", errorToCapture))

		// 6. Add user message if provided
		if len(message) > 0 && message[0] != "" {
			scope.SetContext("Additional Context", map[string]interface{}{
				"message": message[0],
			})
		}

		// Send the error to Sentry with all context
		sentryHub.CaptureException(errorToCapture)
	})
}
