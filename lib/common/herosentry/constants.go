// Package herosentry provides constants used throughout the Hero Sentry SDK.
// This file centralizes all constant values including context keys and
// configuration defaults for better maintainability.
package herosentry

// Context keys for passing data through context.
// These typed constants prevent key collisions and improve type safety.
const (
	// sentryTraceHeader<PERSON><PERSON> is the context key for storing sentry trace headers.
	// This key is used by interceptors to pass trace information through the context.
	sentryTraceHeaderKey contextKey = "sentry-trace-header"

	// httpRequestContextKey is the context key for storing HTTP request objects.
	// This key is used to pass HTTP request data through context for capture functions.
	httpRequestContextKey contextKey = "http.request"
)
