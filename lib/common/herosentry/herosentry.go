// Package herosentry provides the main initialization and configuration for the Hero Sentry SDK.
// This file contains the core initialization function that sets up Sentry with
// environment-aware defaults and automatic configuration.
//
// The SDK is designed to be initialized once at application startup with minimal
// configuration required. It automatically detects the environment and adjusts
// settings accordingly.
//
// Key features:
// - Automatic environment detection from ENVIRONMENT variable
// - Smart sampling rates (100% in dev, 10% in production)
// - Privacy-first configuration (no PII by default)
// - Service name tagging for all events
// - Optional version tracking for releases
package herosentry

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/getsentry/sentry-go"
)

var (
	// serviceName stores the service identifier provided during initialization.
	// This is added as a tag to all events and spans for filtering in Sentry.
	serviceName string

	// initialized tracks whether the SDK has been successfully initialized.
	// Prevents double initialization and enables no-op behavior when not initialized.
	initialized bool

	// globalStateMutex protects concurrent access to serviceName and initialized
	// Uses RWMutex to allow multiple concurrent readers for better performance
	globalStateMutex sync.RWMutex
)

// Config allows customization of herosentry initialization.
// All fields are optional - if not set, defaults will be used.
type Config struct {
	// DataSourceName overrides the SENTRY_DSN environment variable
	DataSourceName string

	// Environment overrides the ENVIRONMENT env var (e.g., "production", "staging")
	Environment string

	// DevelopmentSampleRate sets the sampling rate for development environment (0.0-1.0)
	// Default: 1.0 (100% sampling)
	DevelopmentSampleRate *float64

	// ProductionSampleRate sets the sampling rate for all non-development environments (0.0-1.0)
	// Default: 0.1 (10% sampling)
	ProductionSampleRate *float64

	// Release version for tracking deployments
	// Default: APP_VERSION env var
	Release string

	// SendDefaultPII controls whether to send personally identifiable information
	// Default: false (privacy-first)
	SendDefaultPII bool

	// BeforeSend allows custom event processing
	// Default: adds service tag
	BeforeSend func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event
}

// Init initializes Sentry for the service with automatic configuration.
// This function should be called once at application startup, typically in main().
//
// The function automatically:
// - Detects environment from ENVIRONMENT env var (defaults to "development")
// - Sets appropriate sampling rates (100% dev, 10% production)
// - Configures privacy settings (no PII by default)
// - Adds service name as a tag to all events
// - Configures release tracking if APP_VERSION is set
//
// Parameters:
//   - service: The name of the service (e.g., "workflow-service", "asset-service")
//   - optionalConfig: Optional configuration to override defaults (variadic)
//
// Returns:
//   - error: An error if initialization fails or if already initialized
//
// Environment variables:
//   - ENVIRONMENT: The deployment environment (prod-1/demo-1/development)
//   - SENTRY_DSN: The Sentry project DSN (required)
//   - APP_VERSION: Optional version string for release tracking
//
// Example usage:
//
//	// Basic usage with defaults
//	func main() {
//	    if err := herosentry.Init("workflow-service"); err != nil {
//	        log.Printf("Failed to initialize Sentry: %v", err)
//	    }
//	    defer herosentry.Flush()
//	}
//
//	// Advanced usage with custom config
//	func main() {
//	    devRate := 0.5
//	    prodRate := 0.05
//	    if err := herosentry.Init("workflow-service", herosentry.Config{
//	        Environment: "staging",
//	        DevelopmentSampleRate: &devRate,  // 50% in development
//	        ProductionSampleRate: &prodRate,   // 5% in production/staging
//	        Release: "v2.0.0-beta",
//	    }); err != nil {
//	        log.Printf("Failed to initialize Sentry: %v", err)
//	    }
//	    defer herosentry.Flush()
//	}
//
// The function is idempotent in terms of effects but returns an error
// on subsequent calls to prevent accidental re-initialization.
func Init(service string, optionalConfig ...Config) error {
	// Prevent double initialization and store service name
	globalStateMutex.Lock()
	if initialized {
		globalStateMutex.Unlock()
		return fmt.Errorf("herosentry already initialized")
	}
	// Keep the lock until we finish initialization
	serviceName = service

	// Extract config if provided
	var configuration Config
	if len(optionalConfig) > 0 {
		configuration = optionalConfig[0]
	}

	// Auto-detect environment from config or ENVIRONMENT variable
	environment := configuration.Environment
	if environment == "" {
		environment = os.Getenv("ENVIRONMENT")
		if environment == "" {
			environment = "development"
		}
	}

	// Determine sampling rate based on environment
	var sampleRate float64
	if environment == "development" {
		// Use development sample rate
		if configuration.DevelopmentSampleRate != nil {
			sampleRate = *configuration.DevelopmentSampleRate
		} else {
			sampleRate = 1.0 // Default 100% for development
		}
	} else {
		// Use production sample rate for all non-development environments
		if configuration.ProductionSampleRate != nil {
			sampleRate = *configuration.ProductionSampleRate
		} else {
			sampleRate = 0.1 // Default 10% for production
		}
	}

	// Configure Sentry with Hero-specific defaults
	dataSourceName := configuration.DataSourceName
	if dataSourceName == "" {
		dataSourceName = os.Getenv("SENTRY_DSN")
	}

	// Check if DSN is provided
	if dataSourceName == "" {
		// Log warning but don't fail - allow service to run without Sentry
		fmt.Printf("WARNING: SENTRY_DSN not set. Herosentry will operate in no-op mode.\n")
		// Don't mark as initialized - keep everything as no-op
		globalStateMutex.Unlock()
		return nil
	}

	options := sentry.ClientOptions{
		// DSN from config or environment
		Dsn: dataSourceName,

		// Environment tag for filtering in Sentry UI
		Environment: environment,

		// Server name helps identify the specific service
		ServerName: service,

		// Enable distributed tracing across services
		EnableTracing: true,

		// Dynamic sampling rate based on environment
		TracesSampleRate: sampleRate,

		// Privacy settings from config
		SendDefaultPII: configuration.SendDefaultPII,
	}

	// Set BeforeSend hook
	if configuration.BeforeSend != nil {
		// Use custom BeforeSend but still add service tag
		options.BeforeSend = func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			// Ensure service tag is always added
			if event.Tags == nil {
				event.Tags = make(map[string]string)
			}
			event.Tags["service"] = service

			// Call custom BeforeSend
			return configuration.BeforeSend(event, hint)
		}
	} else {
		// Default BeforeSend hook
		options.BeforeSend = func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			if event.Tags == nil {
				event.Tags = make(map[string]string)
			}
			event.Tags["service"] = service
			return event
		}
	}

	// Optional: Track releases for better issue tracking
	if configuration.Release != "" {
		options.Release = configuration.Release
	} else if version := os.Getenv("APP_VERSION"); version != "" {
		options.Release = version
	}

	// Release lock before calling sentry.Init (which might take time)
	globalStateMutex.Unlock()

	// Initialize Sentry with our configuration
	if err := sentry.Init(options); err != nil {
		return fmt.Errorf("failed to initialize sentry: %w", err)
	}

	// Mark as initialized to enable SDK functionality
	globalStateMutex.Lock()
	initialized = true
	globalStateMutex.Unlock()
	return nil
}

// Flush waits for all events to be sent to Sentry before the program exits.
// This ensures that error reports and spans are not lost during shutdown.
//
// Always call this function with defer in your main function to ensure
// all telemetry is sent even if the program exits unexpectedly.
//
// The function waits up to 2 seconds for pending events to be sent.
// If not all events are sent within this time, it returns and some events
// may be lost. This timeout prevents the shutdown from hanging indefinitely.
//
// This function is safe to call even if Sentry was not initialized.
//
// Example usage:
//
//	func main() {
//	    if err := herosentry.Init("my-service"); err != nil {
//	        log.Printf("Sentry init failed: %v", err)
//	    }
//	    defer herosentry.Flush() // Always flush before exit
//
//	    // Run service...
//	}
//
// For graceful shutdown:
//
//	func main() {
//	    // ... initialization ...
//
//	    // Handle shutdown signals
//	    sigChan := make(chan os.Signal, 1)
//	    signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
//
//	    go func() {
//	        <-sigChan
//	        log.Println("Shutting down...")
//	        herosentry.Flush() // Ensure events are sent
//	        os.Exit(0)
//	    }()
//
//	    // Run service...
//	}
func Flush() {
	// Only flush if initialized
	globalStateMutex.RLock()
	shouldFlush := initialized
	globalStateMutex.RUnlock()

	if shouldFlush {
		// Wait up to 2 seconds for events to be sent
		sentry.Flush(2 * time.Second)
	}
}

// getServiceName returns the configured service name.
// This is an internal helper used by other parts of the SDK
// to tag events with the service name.
//
// Returns:
//   - string: The service name provided during Init(), or empty if not initialized
//
// This function is safe to call before initialization.
func getServiceName() string {
	globalStateMutex.RLock()
	defer globalStateMutex.RUnlock()
	return serviceName
}

// isInitialized returns whether herosentry has been initialized.
// This is an internal helper used to enable no-op behavior
// when Sentry is not configured.
//
// Returns:
//   - bool: true if Init() was called successfully, false otherwise
//
// When false, all SDK operations become no-ops to allow the
// application to run without Sentry (e.g., in development or tests).
func isInitialized() bool {
	globalStateMutex.RLock()
	defer globalStateMutex.RUnlock()
	return initialized
}
