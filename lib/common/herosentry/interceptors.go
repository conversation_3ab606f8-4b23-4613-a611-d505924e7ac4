// Package herosentry provides RPC and HTTP interceptors for automatic distributed tracing.
// This file contains interceptors that automatically handle trace creation, propagation,
// and context enrichment for all incoming and outgoing RPC calls and HTTP requests.
//
// The interceptors handle the complexity of distributed tracing so that service code
// doesn't need to manually manage trace headers or span creation for cross-service calls.
package herosentry

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"connectrpc.com/connect"
	"github.com/getsentry/sentry-go"
)

// RPCServiceInterceptor creates a Connect RPC interceptor for incoming requests.
// This interceptor automatically:
//   - Creates root transactions for new requests
//   - Continues distributed traces from upstream services
//   - Captures service/method names and metadata
//   - Handles errors and panics with proper Sentry reporting
//
// Usage:
//
//	mux.Handle(orderPath,
//	  connect.NewHandler(
//	    orderService,
//	    connect.WithInterceptors(herosentry.RPCServiceInterceptor()),
//	  ),
//	)
//
// For an incoming request to OrderService.CreateOrder:
//   - If no trace header: Creates new transaction "OrderService.CreateOrder"
//   - If trace header present: Continues trace from upstream service
//   - Automatically captures: service name, method, duration, errors
func RPCServiceInterceptor() connect.Interceptor {
	return connect.UnaryInterceptorFunc(
		func(next connect.UnaryFunc) connect.UnaryFunc {
			return func(requestContext context.Context, request connect.AnyRequest) (connect.AnyResponse, error) {
				if !isInitialized() {
					return next(requestContext, request)
				}

				// Extract service and method names from procedure
				// Example: "/hero.orders.v2.OrderService/CreateOrder"
				procedure := request.Spec().Procedure
				serviceName := extractServiceName(procedure)
				methodName := extractMethodName(procedure)
				operationName := fmt.Sprintf("%s.%s", serviceName, methodName)

				// Check for distributed trace header
				var sentrySpan *sentry.Span
				var traceHeader string
				if request.Header() != nil {
					traceHeader = request.Header().Get(sentry.SentryTraceHeader)
				}

				if traceHeader != "" {
					// Continue distributed trace from upstream service
					requestContext = context.WithValue(requestContext, sentryTraceHeaderKey, traceHeader)
					sentrySpan = sentry.StartTransaction(
						requestContext,
						operationName,
						sentry.ContinueFromHeaders(sentry.SentryTraceHeader, traceHeader),
					)
				} else {
					// Start new trace
					sentrySpan = sentry.StartTransaction(requestContext, operationName)
				}
				defer sentrySpan.Finish()

				// Create internal span wrapper and enrich with context
				internalSpanWrapper := &internalSpan{Span: sentrySpan}
				enrichSpanWithContext(internalSpanWrapper, requestContext, 3)

				// Set RPC-specific tags
				internalSpanWrapper.SetTag("rpc.service", serviceName)
				internalSpanWrapper.SetTag("rpc.method", methodName)
				internalSpanWrapper.SetTag("rpc.type", "server")
				internalSpanWrapper.SetTag("span.kind", "server")

				// Update context with span
				requestContext = sentrySpan.Context()

				// Handle panics
				defer func() {
					if recovered := recover(); recovered != nil {
						internalSpanWrapper.SetTag("error", "true")
						internalSpanWrapper.SetTag("error.type", "panic")
						sentry.CaptureException(fmt.Errorf("panic: %v", recovered))
						panic(recovered) // Re-panic after capture
					}
				}()

				// Execute the RPC handler
				response, err := next(requestContext, request)

				// Handle errors
				if err != nil {
					internalSpanWrapper.SetTag("error", "true")
					internalSpanWrapper.SetTag("rpc.status", "error")
					internalSpanWrapper.SetData("error.message", err.Error())

					// Capture Connect error details if available
					if connectErr := new(connect.Error); errors.As(err, &connectErr) {
						internalSpanWrapper.SetTag("rpc.code", connectErr.Code().String())
						internalSpanWrapper.SetData("error.details", connectErr.Details())
					}
				} else {
					internalSpanWrapper.SetTag("rpc.status", "success")
				}

				return response, err
			}
		},
	)
}

// RPCClientInterceptor creates a Connect RPC interceptor for outgoing requests.
// This interceptor automatically:
//   - Creates child spans for outgoing RPC calls
//   - Propagates trace context via headers
//   - Tracks external service performance
//   - Captures failures and latency
//
// Usage:
//
//	assetClient := asset.NewClient(
//	  httpClient,
//	  url,
//	  connect.WithInterceptors(herosentry.RPCClientInterceptor()),
//	)
//
// For an outgoing call to AssetService.GetAsset:
//   - Creates child span "calling AssetService.GetAsset"
//   - Adds header: sentry-trace: {trace-id}-{span-id}-{sampled}
//   - Tracks: target service, method, duration, success/failure
func RPCClientInterceptor() connect.Interceptor {
	return connect.UnaryInterceptorFunc(
		func(next connect.UnaryFunc) connect.UnaryFunc {
			return func(requestContext context.Context, request connect.AnyRequest) (connect.AnyResponse, error) {
				if !isInitialized() {
					return next(requestContext, request)
				}

				// Extract service and method names
				procedure := request.Spec().Procedure
				serviceName := extractServiceName(procedure)
				methodName := extractMethodName(procedure)
				operationName := fmt.Sprintf("calling %s.%s", serviceName, methodName)

				// Start span for outgoing call
				requestContext, _, finishSpan := StartSpan(requestContext, operationName)
				defer finishSpan()

				// Get current span to add tags
				if currentSpan := CurrentSpan(requestContext); currentSpan != nil {
					currentSpan.SetTag("rpc.service", serviceName)
					currentSpan.SetTag("rpc.method", methodName)
					currentSpan.SetTag("rpc.type", "client")
					currentSpan.SetTag("span.kind", "client")
					currentSpan.SetTag("rpc.procedure", procedure)
				}

				// Propagate trace context via headers
				if sentrySpan := sentry.SpanFromContext(requestContext); sentrySpan != nil {
					traceHeader := createTraceHeader(sentrySpan)
					if traceHeader != "" && request.Header() != nil {
						request.Header().Set(sentry.SentryTraceHeader, traceHeader)
					}
				}

				// Make the RPC call
				response, err := next(requestContext, request)

				// Handle response
				if currentSpan := CurrentSpan(requestContext); currentSpan != nil {
					if err != nil {
						currentSpan.SetTag("error", "true")
						currentSpan.SetTag("rpc.status", "error")
						currentSpan.SetData("error.message", err.Error())
					} else {
						currentSpan.SetTag("rpc.status", "success")
					}
				}

				return response, err
			}
		},
	)
}

// HTTPMiddleware creates an HTTP middleware for non-RPC endpoints.
// This middleware automatically:
//   - Creates transactions for incoming HTTP requests
//   - Captures route, method, status codes
//   - Handles panics with proper error reporting
//   - Times request duration
//
// Usage:
//
//	http.Handle("/webhook/twilio", herosentry.HTTPMiddleware(webhookHandler))
//
// For an incoming POST to /webhook/twilio:
//   - Creates transaction "POST /webhook/twilio"
//   - Captures: method, path, status, duration, client IP
//   - Reports panics and errors to Sentry
func HTTPMiddleware(handler http.Handler) http.Handler {
	return http.HandlerFunc(func(responseWriter http.ResponseWriter, httpRequest *http.Request) {
		if !isInitialized() {
			handler.ServeHTTP(responseWriter, httpRequest)
			return
		}

		// Create operation name from method and path
		operationName := fmt.Sprintf("%s %s", httpRequest.Method, httpRequest.URL.Path)

		// Start transaction for HTTP request
		sentrySpan := sentry.StartTransaction(httpRequest.Context(), operationName)
		defer sentrySpan.Finish()

		// Create internal span wrapper and enrich with context
		internalSpanWrapper := &internalSpan{Span: sentrySpan}
		requestContext := sentrySpan.Context()

		// Store HTTP request in context for capture functions
		requestContext = context.WithValue(requestContext, httpRequestContextKey, httpRequest)
		enrichSpanWithContext(internalSpanWrapper, requestContext, 3)

		// Set HTTP-specific tags
		internalSpanWrapper.SetTag("http.method", httpRequest.Method)
		internalSpanWrapper.SetTag("http.path", httpRequest.URL.Path)
		internalSpanWrapper.SetTag("http.url", httpRequest.URL.String())
		internalSpanWrapper.SetTag("span.kind", "server")

		// Create response writer wrapper to capture status code
		wrappedWriter := &responseWriterWrapper{
			ResponseWriter: responseWriter,
			statusCode:     200, // Default if not explicitly set
		}

		// Handle panics
		defer func() {
			if recovered := recover(); recovered != nil {
				internalSpanWrapper.SetTag("error", "true")
				internalSpanWrapper.SetTag("error.type", "panic")
				internalSpanWrapper.SetTag("http.status_code", "500")
				sentry.CaptureException(fmt.Errorf("panic: %v", recovered))

				// Only write error response if not already written
				// This prevents "http: multiple response.WriteHeader calls" panic
				// if the handler already started writing a response before panicking
				if !wrappedWriter.written {
					// Use wrappedWriter to ensure status code is captured by our tracking
					wrappedWriter.WriteHeader(http.StatusInternalServerError)
					wrappedWriter.Write([]byte("Internal Server Error"))
				}

				// Re-panic to maintain proper error propagation
				// Without this, panics would be silently swallowed, breaking debugging
				// and preventing upstream handlers from detecting the failure
				panic(recovered)
			}
		}()

		// Update request with new context
		httpRequest = httpRequest.WithContext(requestContext)

		// Call the actual handler
		handler.ServeHTTP(wrappedWriter, httpRequest)

		// Record final status
		internalSpanWrapper.SetTag("http.status_code", fmt.Sprintf("%d", wrappedWriter.statusCode))
		if wrappedWriter.statusCode >= 400 {
			internalSpanWrapper.SetTag("error", "true")
		}
	})
}

// responseWriterWrapper captures the HTTP status code
type responseWriterWrapper struct {
	http.ResponseWriter
	statusCode int
	written    bool
}

func (wrapper *responseWriterWrapper) WriteHeader(statusCode int) {
	if !wrapper.written {
		wrapper.statusCode = statusCode
		wrapper.written = true
	}
	wrapper.ResponseWriter.WriteHeader(statusCode)
}

func (wrapper *responseWriterWrapper) Write(data []byte) (int, error) {
	if !wrapper.written {
		wrapper.written = true
	}
	return wrapper.ResponseWriter.Write(data)
}

// extractServiceName extracts the service name from a Connect RPC procedure.
//
// Example Input: "/hero.orders.v2.OrderService/CreateOrder"
// Example Output: "OrderService"
func extractServiceName(procedure string) string {
	// Remove leading slash and split by remaining slash
	procedure = strings.TrimPrefix(procedure, "/")
	parts := strings.Split(procedure, "/")
	if len(parts) < 2 {
		return "unknown"
	}

	// Extract service name from full package path
	// "hero.orders.v2.OrderService" -> "OrderService"
	servicePath := parts[0]
	pathComponents := strings.Split(servicePath, ".")
	if len(pathComponents) > 0 {
		return pathComponents[len(pathComponents)-1]
	}

	return "unknown"
}

// extractMethodName extracts the method name from a Connect RPC procedure.
//
// Example Input: "/hero.orders.v2.OrderService/CreateOrder"
// Example Output: "CreateOrder"
func extractMethodName(procedure string) string {
	// Remove leading slash and split
	procedure = strings.TrimPrefix(procedure, "/")
	parts := strings.Split(procedure, "/")
	if len(parts) >= 2 {
		return parts[1]
	}
	return "unknown"
}
