// Package herosentry provides span management for distributed tracing.
// This file contains the core span creation and management functions that
// automatically handle trace propagation and parent-child relationships.
//
// The span system supports three scenarios:
// 1. Creating new root transactions for incoming requests
// 2. Creating child spans within an existing transaction
// 3. Continuing distributed traces from other services
//
// All spans automatically capture rich context including user information,
// code location, and request metadata.
package herosentry

import (
	"context"
	"net/http"

	"github.com/getsentry/sentry-go"
)

// StartSpan starts a new span for operation tracking.
// It automatically determines whether to create a child span (if one exists in context)
// or start a new transaction. For distributed systems, it continues traces from
// incoming requests when trace headers are present.
//
// The function returns an updated context containing the new span and a FinishFunc
// that must be called to complete the span. Always use defer with the FinishFunc.
//
// Parameters:
//   - requestContext: The current context, potentially containing a parent span
//   - operationName: A descriptive name for the operation (e.g., "database.query", "http.request")
//
// Returns:
//   - context.Context: Updated context containing the new span
//   - Span: The created span for adding tags and data
//   - FinishFunc: Function to call when the operation completes
//
// Example usage:
//
//	ctx, span, finish := herosentry.StartSpan(ctx, "order.process")
//	defer finish()
//	span.SetTag("order.id", orderID)
//	span.SetData("order.items", orderItems)
//	// ... perform order processing ...
//
// Span hierarchy example:
//
//	// Root transaction (from HTTP request)
//	ctx, span, finish := StartSpan(ctx, "OrderAPI.CreateOrder")
//	defer finish()
//	span.SetTag("order.type", "express")
//
//	// Child span for validation
//	ctx2, span2, finish2 := StartSpan(ctx, "order.validate")
//	defer finish2()
//	span2.SetTag("validation.result", "passed")
//	// ... validation logic ...
//
//	// Another child span for database operation
//	ctx3, span3, finish3 := StartSpan(ctx, "database.insert")
//	defer finish3()
//	span3.SetTag("db.table", "orders")
//	// ... database logic ...
//
// The function automatically:
// - Captures code location (file, line, function)
// - Extracts user context (username, org ID, IP)
// - Adds environment and service tags
// - Handles distributed trace propagation
func StartSpan(requestContext context.Context, operationName string) (context.Context, Span, FinishFunc) {
	// Return no-op if Sentry is not initialized
	if !isInitialized() {
		return requestContext, &internalSpan{}, func() {}
	}

	// Check if we're already in a span/transaction
	parentSpan := sentry.SpanFromContext(requestContext)

	var sentrySpan *sentry.Span
	var spanType string

	if parentSpan != nil {
		// Case 1: We're inside an existing span - create a child
		// This maintains the parent-child relationship in the trace tree
		sentrySpan = parentSpan.StartChild(operationName)
		spanType = "child_span"
	} else {
		// Case 2: No parent span - check for distributed trace
		if traceHeader := extractTraceHeader(requestContext); traceHeader != "" {
			// Case 2a: Continue from distributed trace
			// This happens when receiving a request from another service
			sentrySpan = sentry.StartTransaction(
				requestContext,
				operationName,
				sentry.ContinueFromHeaders(sentry.SentryTraceHeader, traceHeader),
			)
			spanType = "continued_transaction"
		} else {
			// Case 2b: No existing span or trace - start new transaction
			// This is the root of a new trace tree
			sentrySpan = sentry.StartTransaction(requestContext, operationName)
			spanType = "new_transaction"
		}
	}

	// Create our internal span wrapper that adds Hero-specific functionality
	internalSpanWrapper := &internalSpan{
		Span: sentrySpan,
	}

	// Capture all context automatically
	// Skip 3 stack frames: captureRuntimeInfo, enrichSpanWithContext, and StartSpan
	enrichSpanWithContext(internalSpanWrapper, requestContext, 3)

	// Add span type for debugging and understanding trace structure
	internalSpanWrapper.SetTag("span.type", spanType)

	// Update context with new span for propagation to child operations
	newContext := sentrySpan.Context()

	// Return updated context, span, and finish function
	return newContext, internalSpanWrapper, internalSpanWrapper.finish
}

// CurrentSpan returns the current span from context to add additional data.
// Use this function when you need to enrich an existing span with more information
// without creating a new child span.
//
// Parameters:
//   - requestContext: The context potentially containing a span
//
// Returns:
//   - Span: The current span or a no-op span if none exists
//
// Example usage:
//
//	// Inside a function that already has a span
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    // Add custom tags
//	    span.SetTag("cache.hit", "true")
//	    span.SetTag("cache.key", cacheKey)
//
//	    // Add complex data
//	    span.SetData("cache.metadata", map[string]interface{}{
//	        "size": 1024,
//	        "ttl": 3600,
//	        "compression": "gzip",
//	    })
//
//	    // Add user information if not already captured
//	    span.SetUser(userID, userEmail)
//	}
//
// Common patterns:
//
//	// Pattern 1: Conditional data based on results
//	result, err := doOperation()
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    if err != nil {
//	        span.SetTag("error", "true")
//	        span.SetData("error.message", err.Error())
//	    } else {
//	        span.SetTag("result.status", "success")
//	        span.SetData("result.count", len(result))
//	    }
//	}
//
//	// Pattern 2: Adding SQL query information
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    span.SetTag("db.system", "postgresql")
//	    span.SetTag("db.operation", "SELECT")
//	    span.SetData("db.statement", query)
//	}
func CurrentSpan(requestContext context.Context) Span {
	// Return no-op span if Sentry is not initialized
	if !isInitialized() {
		return &internalSpan{}
	}

	// Extract the current span from context
	sentrySpan := sentry.SpanFromContext(requestContext)
	if sentrySpan == nil {
		// No active span in context, return no-op span
		// This allows safe usage without nil checks
		return &internalSpan{}
	}

	// Wrap the Sentry span in our interface
	return &internalSpan{
		Span: sentrySpan,
	}
}

// extractTraceHeader extracts distributed trace header from context.
// This header is used to continue traces across service boundaries.
//
// The function checks two sources:
// 1. Context value set by RPC interceptors
// 2. HTTP request headers (for HTTP endpoints)
//
// Parameters:
//   - requestContext: The context to search for trace headers
//
// Returns:
//   - string: The sentry-trace header value or empty string if not found
//
// Example trace header format:
//
//	"d4cda95b652f4a1592b449d5929fda1b-6e0c63257de34c92-1"
//	Where:
//	- First part: 32-char trace ID
//	- Second part: 16-char parent span ID
//	- Third part: Sampling decision (1=sampled, 0=not sampled)
//
// This function is called internally by StartSpan to maintain trace continuity.
func extractTraceHeader(requestContext context.Context) string {
	// Method 1: Check if trace header was stored in context by RPC interceptor
	// This is the primary method for RPC services
	if traceHeader, hasHeader := requestContext.Value(sentryTraceHeaderKey).(string); hasHeader {
		return traceHeader
	}

	// Method 2: Check HTTP request headers directly
	// This handles HTTP endpoints and webhooks
	if httpRequest, isHTTPRequest := requestContext.Value(httpRequestContextKey).(*http.Request); isHTTPRequest {
		return httpRequest.Header.Get(sentry.SentryTraceHeader)
	}

	// No trace header found
	return ""
}
