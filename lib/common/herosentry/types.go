// Package herosentry provides core types and interfaces for the Hero Sentry SDK.
// This file defines the fundamental types used throughout the SDK including
// the Span interface for enriching trace data and the FinishFunc for completing spans.
//
// The types in this file are designed to provide a clean, user-friendly API
// while maintaining compatibility with the underlying Sentry SDK.
package herosentry

import "github.com/getsentry/sentry-go"

// contextKey is a type for context keys to avoid collisions.
// This type ensures that our context keys are distinct from other packages
// that might use similar string values.
type contextKey string

// FinishFunc is a function that completes a span when called.
// It should be called using defer to ensure the span is properly closed
// and its duration is recorded, even if the function returns early or panics.
//
// Example usage:
//
//	ctx, finish := herosentry.StartSpan(ctx, "database.query")
//	defer finish() // Always use defer to ensure span completion
//
// The function is safe to call multiple times - subsequent calls are no-ops.
type FinishFunc func()

// Span provides methods to enrich span data with additional context.
// Use this interface to add custom tags, data, and user information to traces.
//
// Spans represent units of work within a distributed system and can be
// either transactions (root spans) or child spans within a transaction.
//
// Example usage:
//
//	if span := herosentry.CurrentSpan(ctx); span != nil {
//	    span.SetTag("cache.hit", "true")
//	    span.SetData("query.sql", "SELECT * FROM orders WHERE id = ?")
//	    span.SetUser("user123", "<EMAIL>")
//	}
type Span interface {
	// SetTag adds a searchable tag to the span.
	// Tags are key-value pairs that can be used for filtering and searching in Sentry.
	// Use tags for low-cardinality fields that you want to search or group by.
	//
	// Common tag examples:
	//   - "http.status_code": "200"
	//   - "db.operation": "SELECT"
	//   - "cache.hit": "true"
	//   - "feature.flag": "new-checkout-flow"
	//
	// Parameters:
	//   - key: The tag name (e.g., "http.method")
	//   - value: The tag value (e.g., "POST")
	//
	// Example:
	//   span.SetTag("payment.method", "credit_card")
	//   span.SetTag("payment.status", "success")
	SetTag(key, value string)

	// SetData adds additional context data to the span.
	// Unlike tags, data can be high-cardinality and include complex objects.
	// Data is not indexed for search but is visible when viewing the span.
	//
	// Common data examples:
	//   - SQL queries
	//   - Request/response bodies
	//   - Configuration values
	//   - Detailed error information
	//
	// Parameters:
	//   - key: The data field name (e.g., "sql.query")
	//   - value: Any JSON-serializable value
	//
	// Example:
	//   span.SetData("request.body", map[string]interface{}{
	//       "product_id": "SKU-123",
	//       "quantity": 2,
	//       "price": 49.99,
	//   })
	//   span.SetData("sql.query", "INSERT INTO orders (id, total) VALUES (?, ?)")
	SetData(key string, value interface{})

	// SetUser sets user information on the span.
	// This helps identify which user triggered the operation.
	// The user information is automatically indexed and searchable in Sentry.
	//
	// Parameters:
	//   - id: The user's unique identifier (e.g., database ID, cognito ID)
	//   - email: The user's email address (optional, pass empty string if not available)
	//
	// Example:
	//   span.SetUser("user-12345", "<EMAIL>")
	//   span.SetUser(userID, "") // Email unknown
	//
	// Note: User context from authentication (cognito ID, etc.) is automatically
	// captured by the SDK. Use this method only when you need to override or
	// add additional user information.
	SetUser(id, email string)
}

// internalSpan wraps sentry.Span with Hero-specific enhancements.
// This internal type implements the Span interface and adds automatic
// context capture from the Hero Core framework.
//
// The struct maintains both the underlying Sentry span and cached values
// of auto-captured context to avoid repeated lookups.
type internalSpan struct {
	// Embedded Sentry span for core functionality
	*sentry.Span

	// Auto-captured fields from Hero context
	// These fields are populated automatically when a span is created

	// Code location information
	file     string // Source file path (e.g., "services/workflow/internal/usecase.go")
	line     int    // Line number where span was created
	function string // Function name (e.g., "(*OrderUsecase).CreateOrder")

	// Authentication context
	username  string // From cmncontext.GetUsername (e.g., "cognito:abc123", "bot:worker")
	orgID     int32  // Organization ID from cmncontext.GetOrgId
	ipAddress string // Client IP from cmncontext.GetIPAddress

	// Additional context
	assetID     string // Asset ID if available from database lookup
	requestID   string // Request correlation ID from headers (X-Request-ID, etc.)
	environment string // Deployment environment from ENVIRONMENT env var
}

// SetTag adds a searchable tag to the span.
// Tags are indexed by Sentry and can be used for filtering and grouping.
//
// This method is safe to call on a nil span or when Sentry is not initialized.
// In such cases, the operation is a no-op.
//
// Example input/output:
//
//	Input:  tagKey="http.method", tagValue="POST"
//	Result: Adds "http.method: POST" tag to the span in Sentry
//
//	Input:  tagKey="db.name", tagValue="postgres"
//	Result: Adds "db.name: postgres" tag to the span in Sentry
func (spanInstance *internalSpan) SetTag(tagKey, tagValue string) {
	if spanInstance.Span != nil {
		spanInstance.Span.SetTag(tagKey, tagValue)
	}
}

// SetData adds additional context data to the span.
// Unlike tags, data can include complex objects and is not indexed for search.
// The data is visible when viewing the span details in Sentry.
//
// This method is safe to call on a nil span or when Sentry is not initialized.
// In such cases, the operation is a no-op.
//
// Example input/output:
//
//	Input:  dataKey="request.body", dataValue=map[string]interface{}{"user_id": 123, "action": "purchase"}
//	Result: Attaches the request body object to the span
//
//	Input:  dataKey="sql.query", dataValue="SELECT * FROM users WHERE id = $1"
//	Result: Attaches the SQL query string to the span
//
//	Input:  dataKey="cache.size", dataValue=1024
//	Result: Attaches the cache size number to the span
func (spanInstance *internalSpan) SetData(dataKey string, dataValue interface{}) {
	if spanInstance.Span != nil {
		spanInstance.Span.SetData(dataKey, dataValue)
	}
}

// SetUser sets user information on the span.
// The user ID is always set, while email is only set if provided.
// This information helps identify which user triggered the operation.
//
// This method is safe to call on a nil span or when Sentry is not initialized.
// In such cases, the operation is a no-op.
//
// Example input/output:
//
//	Input:  userID="user-12345", userEmail="<EMAIL>"
//	Result: Adds tags "user.id: user-12345" and "user.email: <EMAIL>"
//
//	Input:  userID="cognito:abc-123", userEmail=""
//	Result: Adds tag "user.id: cognito:abc-123" (no email tag added)
//
// Note: The SDK automatically captures user context from authentication.
// Use this method only when you need to override or add specific user information.
func (spanInstance *internalSpan) SetUser(userID, userEmail string) {
	if spanInstance.Span != nil {
		spanInstance.Span.SetTag("user.id", userID)
		if userEmail != "" {
			spanInstance.Span.SetTag("user.email", userEmail)
		}
	}
}

// finish completes the span and records its duration.
// This method is called by the FinishFunc returned from StartSpan.
//
// The method is safe to call multiple times - subsequent calls are no-ops.
// This ensures that deferred finish calls don't cause issues.
//
// When called, it:
// 1. Marks the span as complete
// 2. Records the total duration since span creation
// 3. Sends the span data to Sentry
//
// Example timeline:
//
//	T0: StartSpan called -> span created
//	T1: Various operations performed
//	T2: finish() called -> duration = T2-T0 recorded
func (spanInstance *internalSpan) finish() {
	if spanInstance.Span != nil {
		spanInstance.Span.Finish()
	}
}

// TraceContext holds distributed tracing information for cross-service communication.
// This struct contains the essential fields needed to continue a trace
// in another service or process.
//
// Example usage:
//
//	// Service A: Extract trace context
//	traceCtx := extractTraceContext(ctx)
//
//	// Send to Service B (e.g., via message queue)
//	message.TraceContext = traceCtx
//
//	// Service B: Continue trace
//	ctx = continueTrace(ctx, message.TraceContext)
type TraceContext struct {
	// TraceID is the unique identifier for the entire trace across all services.
	// Format: 32-character hex string (e.g., "d4cda95b652f4a1592b449d5929fda1b")
	// This ID links all spans belonging to the same user request.
	TraceID string

	// ParentSpanID is the ID of the span that created this trace context.
	// Format: 16-character hex string (e.g., "6e0c63257de34c92")
	// Used to establish parent-child relationships between spans.
	ParentSpanID string

	// Sampled indicates whether this trace should be recorded.
	// When false, the trace is propagated but not sent to Sentry (for performance).
	// Sampling decisions are made at the trace root and inherited by all spans.
	Sampled bool
}
