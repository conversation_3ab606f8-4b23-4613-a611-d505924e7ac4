// @generated by protoc-gen-es v2.6.1 with parameter "target=ts"
// @generated from file hero/assets/v1/assets.proto (package hero.assets.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_hero_situations_v1_situations } from "../../situations/v1/situations_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/assets/v1/assets.proto.
 */
export const file_hero_assets_v1_assets: GenFile = /*@__PURE__*/
  fileDesc("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", [file_hero_situations_v1_situations, file_google_protobuf_timestamp]);

/**
 * @generated from message hero.assets.v1.GetAssetPrivateRequest
 */
export type GetAssetPrivateRequest = Message<"hero.assets.v1.GetAssetPrivateRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;
};

/**
 * Describes the message hero.assets.v1.GetAssetPrivateRequest.
 * Use `create(GetAssetPrivateRequestSchema)` to create a new message.
 */
export const GetAssetPrivateRequestSchema: GenMessage<GetAssetPrivateRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 0);

/**
 * @generated from message hero.assets.v1.GetAssetPrivateResponse
 */
export type GetAssetPrivateResponse = Message<"hero.assets.v1.GetAssetPrivateResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Asset asset = 1;
   */
  asset?: Asset;

  /**
   * @generated from field: hero.assets.v1.ZelloCreds zello_creds = 2;
   */
  zelloCreds?: ZelloCreds;
};

/**
 * Describes the message hero.assets.v1.GetAssetPrivateResponse.
 * Use `create(GetAssetPrivateResponseSchema)` to create a new message.
 */
export const GetAssetPrivateResponseSchema: GenMessage<GetAssetPrivateResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 1);

/**
 * @generated from message hero.assets.v1.GetAssetByCognitoSubRequest
 */
export type GetAssetByCognitoSubRequest = Message<"hero.assets.v1.GetAssetByCognitoSubRequest"> & {
  /**
   * @generated from field: string cognito_jwt_sub = 1;
   */
  cognitoJwtSub: string;
};

/**
 * Describes the message hero.assets.v1.GetAssetByCognitoSubRequest.
 * Use `create(GetAssetByCognitoSubRequestSchema)` to create a new message.
 */
export const GetAssetByCognitoSubRequestSchema: GenMessage<GetAssetByCognitoSubRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 2);

/**
 * @generated from message hero.assets.v1.GetAssetByCognitoSubResponse
 */
export type GetAssetByCognitoSubResponse = Message<"hero.assets.v1.GetAssetByCognitoSubResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v1.GetAssetByCognitoSubResponse.
 * Use `create(GetAssetByCognitoSubResponseSchema)` to create a new message.
 */
export const GetAssetByCognitoSubResponseSchema: GenMessage<GetAssetByCognitoSubResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 3);

/**
 * @generated from message hero.assets.v1.GetAssetByPhoneNumberRequest
 */
export type GetAssetByPhoneNumberRequest = Message<"hero.assets.v1.GetAssetByPhoneNumberRequest"> & {
  /**
   * @generated from field: string phone_number = 1;
   */
  phoneNumber: string;
};

/**
 * Describes the message hero.assets.v1.GetAssetByPhoneNumberRequest.
 * Use `create(GetAssetByPhoneNumberRequestSchema)` to create a new message.
 */
export const GetAssetByPhoneNumberRequestSchema: GenMessage<GetAssetByPhoneNumberRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 4);

/**
 * @generated from message hero.assets.v1.GetAssetByPhoneNumberResponse
 */
export type GetAssetByPhoneNumberResponse = Message<"hero.assets.v1.GetAssetByPhoneNumberResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v1.GetAssetByPhoneNumberResponse.
 * Use `create(GetAssetByPhoneNumberResponseSchema)` to create a new message.
 */
export const GetAssetByPhoneNumberResponseSchema: GenMessage<GetAssetByPhoneNumberResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 5);

/**
 * @generated from message hero.assets.v1.UpdateOrderRequest
 */
export type UpdateOrderRequest = Message<"hero.assets.v1.UpdateOrderRequest"> & {
  /**
   * @generated from field: hero.assets.v1.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.assets.v1.UpdateOrderRequest.
 * Use `create(UpdateOrderRequestSchema)` to create a new message.
 */
export const UpdateOrderRequestSchema: GenMessage<UpdateOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 6);

/**
 * @generated from message hero.assets.v1.UpdateOrderResponse
 */
export type UpdateOrderResponse = Message<"hero.assets.v1.UpdateOrderResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.assets.v1.UpdateOrderResponse.
 * Use `create(UpdateOrderResponseSchema)` to create a new message.
 */
export const UpdateOrderResponseSchema: GenMessage<UpdateOrderResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 7);

/**
 * @generated from message hero.assets.v1.CompleteOrderRequest
 */
export type CompleteOrderRequest = Message<"hero.assets.v1.CompleteOrderRequest"> & {
  /**
   * @generated from field: bool completed = 1;
   */
  completed: boolean;

  /**
   * @generated from field: string order_id = 2;
   */
  orderId: string;
};

/**
 * Describes the message hero.assets.v1.CompleteOrderRequest.
 * Use `create(CompleteOrderRequestSchema)` to create a new message.
 */
export const CompleteOrderRequestSchema: GenMessage<CompleteOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 8);

/**
 * @generated from message hero.assets.v1.CompleteOrderResponse
 */
export type CompleteOrderResponse = Message<"hero.assets.v1.CompleteOrderResponse"> & {
};

/**
 * Describes the message hero.assets.v1.CompleteOrderResponse.
 * Use `create(CompleteOrderResponseSchema)` to create a new message.
 */
export const CompleteOrderResponseSchema: GenMessage<CompleteOrderResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 9);

/**
 * @generated from message hero.assets.v1.PingRequest
 */
export type PingRequest = Message<"hero.assets.v1.PingRequest"> & {
  /**
   * @generated from field: string asset_id = 1;
   */
  assetId: string;

  /**
   * Latitude in decimal degrees
   *
   * @generated from field: double latitude = 2;
   */
  latitude: number;

  /**
   * Longitude in decimal degrees
   *
   * @generated from field: double longitude = 3;
   */
  longitude: number;
};

/**
 * Describes the message hero.assets.v1.PingRequest.
 * Use `create(PingRequestSchema)` to create a new message.
 */
export const PingRequestSchema: GenMessage<PingRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 10);

/**
 * @generated from message hero.assets.v1.PingResponse
 */
export type PingResponse = Message<"hero.assets.v1.PingResponse"> & {
};

/**
 * Describes the message hero.assets.v1.PingResponse.
 * Use `create(PingResponseSchema)` to create a new message.
 */
export const PingResponseSchema: GenMessage<PingResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 11);

/**
 * @generated from message hero.assets.v1.AckOrderRequest
 */
export type AckOrderRequest = Message<"hero.assets.v1.AckOrderRequest"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: bool accepted = 2;
   */
  accepted: boolean;
};

/**
 * Describes the message hero.assets.v1.AckOrderRequest.
 * Use `create(AckOrderRequestSchema)` to create a new message.
 */
export const AckOrderRequestSchema: GenMessage<AckOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 12);

/**
 * @generated from message hero.assets.v1.AckOrderResponse
 */
export type AckOrderResponse = Message<"hero.assets.v1.AckOrderResponse"> & {
};

/**
 * Describes the message hero.assets.v1.AckOrderResponse.
 * Use `create(AckOrderResponseSchema)` to create a new message.
 */
export const AckOrderResponseSchema: GenMessage<AckOrderResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 13);

/**
 * @generated from message hero.assets.v1.GetAssetRequest
 */
export type GetAssetRequest = Message<"hero.assets.v1.GetAssetRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.assets.v1.GetAssetRequest.
 * Use `create(GetAssetRequestSchema)` to create a new message.
 */
export const GetAssetRequestSchema: GenMessage<GetAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 14);

/**
 * @generated from message hero.assets.v1.GetAssetResponse
 */
export type GetAssetResponse = Message<"hero.assets.v1.GetAssetResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v1.GetAssetResponse.
 * Use `create(GetAssetResponseSchema)` to create a new message.
 */
export const GetAssetResponseSchema: GenMessage<GetAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 15);

/**
 * @generated from message hero.assets.v1.CreateAssetRequest
 */
export type CreateAssetRequest = Message<"hero.assets.v1.CreateAssetRequest"> & {
  /**
   * @generated from field: hero.assets.v1.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v1.CreateAssetRequest.
 * Use `create(CreateAssetRequestSchema)` to create a new message.
 */
export const CreateAssetRequestSchema: GenMessage<CreateAssetRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 16);

/**
 * @generated from message hero.assets.v1.CreateAssetResponse
 */
export type CreateAssetResponse = Message<"hero.assets.v1.CreateAssetResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Asset asset = 1;
   */
  asset?: Asset;
};

/**
 * Describes the message hero.assets.v1.CreateAssetResponse.
 * Use `create(CreateAssetResponseSchema)` to create a new message.
 */
export const CreateAssetResponseSchema: GenMessage<CreateAssetResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 17);

/**
 * @generated from message hero.assets.v1.ListAssetsRequest
 */
export type ListAssetsRequest = Message<"hero.assets.v1.ListAssetsRequest"> & {
};

/**
 * Describes the message hero.assets.v1.ListAssetsRequest.
 * Use `create(ListAssetsRequestSchema)` to create a new message.
 */
export const ListAssetsRequestSchema: GenMessage<ListAssetsRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 18);

/**
 * @generated from message hero.assets.v1.ListAssetsResponse
 */
export type ListAssetsResponse = Message<"hero.assets.v1.ListAssetsResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v1.Asset assets = 1;
   */
  assets: Asset[];
};

/**
 * Describes the message hero.assets.v1.ListAssetsResponse.
 * Use `create(ListAssetsResponseSchema)` to create a new message.
 */
export const ListAssetsResponseSchema: GenMessage<ListAssetsResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 19);

/**
 * @generated from message hero.assets.v1.CreateOrderRequest
 */
export type CreateOrderRequest = Message<"hero.assets.v1.CreateOrderRequest"> & {
  /**
   * @generated from field: hero.assets.v1.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.assets.v1.CreateOrderRequest.
 * Use `create(CreateOrderRequestSchema)` to create a new message.
 */
export const CreateOrderRequestSchema: GenMessage<CreateOrderRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 20);

/**
 * @generated from message hero.assets.v1.CreateOrderResponse
 */
export type CreateOrderResponse = Message<"hero.assets.v1.CreateOrderResponse"> & {
  /**
   * @generated from field: hero.assets.v1.Order order = 1;
   */
  order?: Order;
};

/**
 * Describes the message hero.assets.v1.CreateOrderResponse.
 * Use `create(CreateOrderResponseSchema)` to create a new message.
 */
export const CreateOrderResponseSchema: GenMessage<CreateOrderResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 21);

/**
 * @generated from message hero.assets.v1.ListOrdersForSituationRequest
 */
export type ListOrdersForSituationRequest = Message<"hero.assets.v1.ListOrdersForSituationRequest"> & {
  /**
   * @generated from field: string situation_id = 1;
   */
  situationId: string;
};

/**
 * Describes the message hero.assets.v1.ListOrdersForSituationRequest.
 * Use `create(ListOrdersForSituationRequestSchema)` to create a new message.
 */
export const ListOrdersForSituationRequestSchema: GenMessage<ListOrdersForSituationRequest> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 22);

/**
 * @generated from message hero.assets.v1.ListOrdersForSituationResponse
 */
export type ListOrdersForSituationResponse = Message<"hero.assets.v1.ListOrdersForSituationResponse"> & {
  /**
   * @generated from field: repeated hero.assets.v1.Order orders = 1;
   */
  orders: Order[];
};

/**
 * Describes the message hero.assets.v1.ListOrdersForSituationResponse.
 * Use `create(ListOrdersForSituationResponseSchema)` to create a new message.
 */
export const ListOrdersForSituationResponseSchema: GenMessage<ListOrdersForSituationResponse> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 23);

/**
 * @generated from message hero.assets.v1.Asset
 */
export type Asset = Message<"hero.assets.v1.Asset"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string cognito_jwt_sub = 10;
   */
  cognitoJwtSub: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: hero.assets.v1.AssetType type = 3;
   */
  type: AssetType;

  /**
   * last known location
   *
   * @generated from field: double latitude = 4;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 5;
   */
  longitude: number;

  /**
   * what should it be doing?
   *
   * @generated from field: repeated hero.assets.v1.Order orders = 6;
   */
  orders: Order[];

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 7;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp update_time = 8;
   */
  updateTime?: Timestamp;
};

/**
 * Describes the message hero.assets.v1.Asset.
 * Use `create(AssetSchema)` to create a new message.
 */
export const AssetSchema: GenMessage<Asset> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 24);

/**
 * @generated from message hero.assets.v1.ZelloCreds
 */
export type ZelloCreds = Message<"hero.assets.v1.ZelloCreds"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string asset_id = 2;
   */
  assetId: string;

  /**
   * @generated from field: string username = 3;
   */
  username: string;

  /**
   * @generated from field: string password = 4;
   */
  password: string;

  /**
   * @generated from field: string encrypted_password = 5;
   */
  encryptedPassword: string;
};

/**
 * Describes the message hero.assets.v1.ZelloCreds.
 * Use `create(ZelloCredsSchema)` to create a new message.
 */
export const ZelloCredsSchema: GenMessage<ZelloCreds> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 25);

/**
 * @generated from message hero.assets.v1.Order
 */
export type Order = Message<"hero.assets.v1.Order"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string situation_id = 2;
   */
  situationId: string;

  /**
   * @generated from field: string asset_id = 3;
   */
  assetId: string;

  /**
   * @generated from field: string report_id = 7;
   */
  reportId: string;

  /**
   * Single STATUS field replaces separate bools
   *
   * @generated from field: hero.assets.v1.OrderStatus status = 4;
   */
  status: OrderStatus;

  /**
   * @generated from field: google.protobuf.Timestamp complete_time = 13;
   */
  completeTime?: Timestamp;
};

/**
 * Describes the message hero.assets.v1.Order.
 * Use `create(OrderSchema)` to create a new message.
 */
export const OrderSchema: GenMessage<Order> = /*@__PURE__*/
  messageDesc(file_hero_assets_v1_assets, 26);

/**
 * @generated from enum hero.assets.v1.AssetType
 */
export enum AssetType {
  /**
   * @generated from enum value: ASSET_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: ASSET_TYPE_USER = 1;
   */
  USER = 1,

  /**
   * @generated from enum value: ASSET_TYPE_AGENT = 2;
   */
  AGENT = 2,

  /**
   * @generated from enum value: ASSET_TYPE_CAMERA = 3;
   */
  CAMERA = 3,
}

/**
 * Describes the enum hero.assets.v1.AssetType.
 */
export const AssetTypeSchema: GenEnum<AssetType> = /*@__PURE__*/
  enumDesc(file_hero_assets_v1_assets, 0);

/**
 * Represents the single valid STATUS of the Order at a time
 *
 * @generated from enum hero.assets.v1.OrderStatus
 */
export enum OrderStatus {
  /**
   * @generated from enum value: ORDER_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Examples of potential STATUSs:
   *
   * e.g., created but not acknowledged
   *
   * @generated from enum value: ORDER_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * e.g., acknowledged by the asset
   *
   * @generated from enum value: ORDER_STATUS_ACKED = 2;
   */
  ACKED = 2,

  /**
   * e.g., assisting 
   *
   * @generated from enum value: ORDER_STATUS_ASSISTING = 3;
   */
  ASSISTING = 3,

  /**
   * e.g., completed
   *
   * @generated from enum value: ORDER_STATUS_COMPLETED = 4;
   */
  COMPLETED = 4,

  /**
   * rejected
   *
   * @generated from enum value: ORDER_STATUS_REJECTED = 5;
   */
  REJECTED = 5,
}

/**
 * Describes the enum hero.assets.v1.OrderStatus.
 */
export const OrderStatusSchema: GenEnum<OrderStatus> = /*@__PURE__*/
  enumDesc(file_hero_assets_v1_assets, 1);

/**
 * @generated from enum hero.assets.v1.OrderType
 */
export enum OrderType {
  /**
   * @generated from enum value: ORDER_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: ORDER_TYPE_RESCUE = 1;
   */
  RESCUE = 1,
}

/**
 * Describes the enum hero.assets.v1.OrderType.
 */
export const OrderTypeSchema: GenEnum<OrderType> = /*@__PURE__*/
  enumDesc(file_hero_assets_v1_assets, 2);

/**
 * @generated from service hero.assets.v1.AssetRegistryService
 */
export const AssetRegistryService: GenService<{
  /**
   * @generated from rpc hero.assets.v1.AssetRegistryService.GetAsset
   */
  getAsset: {
    methodKind: "unary";
    input: typeof GetAssetRequestSchema;
    output: typeof GetAssetResponseSchema;
  },
  /**
   * Fetch an asset by its Cognito JWT `sub` claim
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.GetAssetByCognitoSub
   */
  getAssetByCognitoSub: {
    methodKind: "unary";
    input: typeof GetAssetByCognitoSubRequestSchema;
    output: typeof GetAssetByCognitoSubResponseSchema;
  },
  /**
   * Fetch an asset by its phone number
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.GetAssetByPhoneNumber
   */
  getAssetByPhoneNumber: {
    methodKind: "unary";
    input: typeof GetAssetByPhoneNumberRequestSchema;
    output: typeof GetAssetByPhoneNumberResponseSchema;
  },
  /**
   * Register a new Asset to the Registry
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.CreateAsset
   */
  createAsset: {
    methodKind: "unary";
    input: typeof CreateAssetRequestSchema;
    output: typeof CreateAssetResponseSchema;
  },
  /**
   * @generated from rpc hero.assets.v1.AssetRegistryService.CreateResponderAsset
   */
  createResponderAsset: {
    methodKind: "unary";
    input: typeof CreateAssetRequestSchema;
    output: typeof CreateAssetResponseSchema;
  },
  /**
   * List all Assets, with their current Orders
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.ListAssets
   */
  listAssets: {
    methodKind: "unary";
    input: typeof ListAssetsRequestSchema;
    output: typeof ListAssetsResponseSchema;
  },
  /**
   * @generated from rpc hero.assets.v1.AssetRegistryService.CreateOrder
   */
  createOrder: {
    methodKind: "unary";
    input: typeof CreateOrderRequestSchema;
    output: typeof CreateOrderResponseSchema;
  },
  /**
   * @generated from rpc hero.assets.v1.AssetRegistryService.ListOrdersForSituation
   */
  listOrdersForSituation: {
    methodKind: "unary";
    input: typeof ListOrdersForSituationRequestSchema;
    output: typeof ListOrdersForSituationResponseSchema;
  },
  /**
   * Assets are expected to Ping their status continuously while online
   * In response, they will receive their current orders, if any
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.Ping
   */
  ping: {
    methodKind: "unary";
    input: typeof PingRequestSchema;
    output: typeof PingResponseSchema;
  },
  /**
   * @generated from rpc hero.assets.v1.AssetRegistryService.CompleteOrder
   */
  completeOrder: {
    methodKind: "unary";
    input: typeof CompleteOrderRequestSchema;
    output: typeof CompleteOrderResponseSchema;
  },
  /**
   * @generated from rpc hero.assets.v1.AssetRegistryService.AckOrder
   */
  ackOrder: {
    methodKind: "unary";
    input: typeof AckOrderRequestSchema;
    output: typeof AckOrderResponseSchema;
  },
  /**
   * New RPC to update an existing order (e.g., to change status or other fields)
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.UpdateOrder
   */
  updateOrder: {
    methodKind: "unary";
    input: typeof UpdateOrderRequestSchema;
    output: typeof UpdateOrderResponseSchema;
  },
  /**
   * GetAssetPrivate is a special RPC to fetch an Asset and its private details
   * (e.g., Zello credentials) for use in secure communications
   * It is intended to only be accessed by the Asset itself, or by Admins
   *
   * @generated from rpc hero.assets.v1.AssetRegistryService.GetAssetPrivate
   */
  getAssetPrivate: {
    methodKind: "unary";
    input: typeof GetAssetPrivateRequestSchema;
    output: typeof GetAssetPrivateResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_assets_v1_assets, 0);

